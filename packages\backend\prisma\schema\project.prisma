model Project {
    id             String          @id @default(uuid())
    name           String
    description    String?
    platform       ProjectPlatform
    ai             String?
    orm            String?
    database       String?
    auth           String?
    framework      String?
    infrastructure String?
    dueDate        DateTime?
    status         ProjectStatus?
    idea           Idea?           @relation(fields: [ideaId], references: [id], onDelete: Cascade)
    ideaId         String?
    createdAt      DateTime        @default(now())
    updatedAt      DateTime        @updatedAt
    organization   Organization?   @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    organizationId String?
    issues         Issue[]
    createdBy      User?           @relation("CreatedProjects", fields: [createdById], references: [id], onDelete: SetNull)
    createdById    String?
    assets         Asset[]
    publicRoadmaps PublicRoadmap[]
    waitlists      Waitlist[]
    features       Feature[]
    milestones     Milestone[]

    @@index([ideaId])
    @@index([organizationId])
    @@index([organizationId, status])
    @@index([organizationId, dueDate])
    @@index([createdById])
}

model Issue {
    id               String       @id @default(uuid())
    title            String
    description      String?
    organization     Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    organizationId   String
    project          Project      @relation(fields: [projectId], references: [id], onDelete: Cascade)
    projectId        String
    milestone        Milestone?   @relation(fields: [milestoneId], references: [id], onDelete: SetNull)
    milestoneId      String?
    featureId        String?
    parentIssueId    String?
    status           IssueStatus
    priority         Importance
    label            IssueLabel
    dueDate          DateTime?
    assignedTo       User?        @relation("AssignedIssues", fields: [assignedToId], references: [id], onDelete: SetNull)
    assignedToId     String?
    achieved         Boolean?
    isPublic         Boolean?
    sourceType       String?
    sourceFeedbackId String?

    // Self-referencing relationships for parent/child issues
    parentIssue Issue?  @relation("IssueHierarchy", fields: [parentIssueId], references: [id], onDelete: SetNull)
    subIssues   Issue[] @relation("IssueHierarchy")

    // Issue dependencies
    dependencies IssueDependency[] @relation("Dependants")
    dependentOn  IssueDependency[] @relation("Dependencies")

    // Issue links
    links IssueLink[]

    // Feedback conversion
    convertedFromFeedback       RoadmapFeedback[] @relation("FeedbackToIssue")
    convertedFromFeatureRequest FeatureRequest[]  @relation("FeatureRequestToIssue")

    // Changelog entries
    changelogEntries ChangelogEntry[]

    @@index([organizationId, achieved])
    @@index([projectId, organizationId, achieved])
    @@index([milestoneId])
    @@index([parentIssueId])
    @@index([assignedToId])
}

model IssueDependency {
    id             String   @id @default(uuid())
    organizationId String
    issueId        String
    dependencyId   String
    createdAt      DateTime @default(now())

    // Relations
    organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    issue        Issue        @relation("Dependants", fields: [issueId], references: [id], onDelete: Cascade)
    dependency   Issue        @relation("Dependencies", fields: [dependencyId], references: [id], onDelete: Cascade)

    @@index([organizationId])
    @@index([issueId])
    @@index([dependencyId])
    @@index([organizationId, issueId])
    @@index([organizationId, dependencyId])
    @@map("issueDependency")
}

model IssueLink {
    id             String       @id @default(uuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    issueId        String
    issue          Issue        @relation(fields: [issueId], references: [id], onDelete: Cascade)
    url            String
    createdAt      DateTime     @default(now())

    @@index([organizationId])
    @@index([issueId])
    @@index([organizationId, issueId])
    @@map("issueLink")
}

model Asset {
    id             String         @id @default(uuid())
    name           String
    description    String?
    type           AssetType
    project        Project        @relation(fields: [projectId], references: [id], onDelete: Cascade)
    projectId      String
    organization   Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    organizationId String
    storageId      String?
    fileName       String?
    fileSize       Int?
    mimeType       String?
    url            String?
    linkType       LinkType?
    tags           String[]
    category       AssetCategory?
    thumbnailUrl   String?
    isPublic       Boolean?
    uploadedBy     User?          @relation("UploadedAssets", fields: [uploadedById], references: [id], onDelete: SetNull)
    uploadedById   String?
    createdAt      DateTime       @default(now())
    updatedAt      DateTime       @updatedAt

    // Relations
    assetViews     AssetView[]
    assetDownloads AssetDownload[]

    @@index([projectId])
    @@index([organizationId])
    @@index([type])
    @@index([category])
    @@index([uploadedById])
    @@index([createdAt])
    @@index([projectId, type])
    @@index([projectId, category])
}

model ActivityFeed {
    id             String       @id @default(uuid())
    type           ActivityType
    title          String
    description    String?
    entityType     EntityType
    entityId       String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    organizationId String
    user           User?        @relation(fields: [userId], references: [id], onDelete: SetNull)
    userId         String?
    oldValue       String?
    newValue       String?
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt

    @@index([entityType, entityId])
    @@index([organizationId])
    @@index([userId])
    @@index([createdAt])
    @@index([entityType, entityId, createdAt])
}

model PublicRoadmap {
    id              String             @id @default(uuid())
    project         Project            @relation(fields: [projectId], references: [id], onDelete: Cascade)
    projectId       String
    name            String
    slug            String
    description     String
    isPublic        Boolean
    allowVoting     Boolean
    allowFeedback   Boolean
    createdAt       DateTime           @default(now())
    updatedAt       DateTime           @updatedAt
    items           RoadmapItem[]
    changelogs      RoadmapChangelog[]
    featureRequests FeatureRequest[]

    @@index([projectId])
    @@index([slug])
}

model RoadmapItem {
    id          String            @id @default(uuid())
    roadmap     PublicRoadmap     @relation(fields: [roadmapId], references: [id], onDelete: Cascade)
    roadmapId   String
    title       String
    description String
    status      IssueStatus
    category    IssueLabel
    isPublic    Boolean
    priority    Importance
    targetDate  DateTime?
    createdAt   DateTime          @default(now())
    updatedAt   DateTime          @updatedAt
    votes       RoadmapVote[]
    feedback    RoadmapFeedback[]

    // Feature request conversion
    convertedFromFeatureRequest FeatureRequest[] @relation("FeatureRequestToRoadmapItem")

    @@index([roadmapId])
    @@index([roadmapId, status])
    @@index([roadmapId, category])
}

model RoadmapVote {
    id            String      @id @default(uuid())
    roadmapItem   RoadmapItem @relation(fields: [roadmapItemId], references: [id], onDelete: Cascade)
    roadmapItemId String
    userId        String?
    ipAddress     String
    createdAt     DateTime

    @@index([roadmapItemId])
    @@index([roadmapItemId, ipAddress])
    @@index([roadmapItemId, userId])
}

model RoadmapFeedback {
    id                   String                   @id @default(uuid())
    roadmapItem          RoadmapItem              @relation(fields: [roadmapItemId], references: [id], onDelete: Cascade)
    roadmapItemId        String
    userId               String?
    ipAddress            String
    content              String
    sentiment            RoadmapFeedbackSentiment
    isApproved           Boolean
    convertedToFeatureId String?
    convertedToIssueId   String?
    convertedAt          DateTime?
    convertedBy          String?
    conversionNotes      String?
    createdAt            DateTime

    // Relations to converted items
    convertedFeature Feature? @relation("FeedbackToFeature", fields: [convertedToFeatureId], references: [id], onDelete: SetNull)
    convertedIssue   Issue?   @relation("FeedbackToIssue", fields: [convertedToIssueId], references: [id], onDelete: SetNull)

    @@index([roadmapItemId])
    @@index([roadmapItemId, ipAddress])
    @@index([roadmapItemId, userId])
    @@index([convertedToFeatureId])
    @@index([convertedToIssueId])
}

model RoadmapChangelog {
    id          String        @id @default(uuid())
    roadmap     PublicRoadmap @relation(fields: [roadmapId], references: [id], onDelete: Cascade)
    roadmapId   String
    title       String
    description String
    version     String? // e.g., "1.2.0", "v2.1.0"
    publishDate DateTime
    isPublished Boolean       @default(false)
    createdAt   DateTime      @default(now())
    updatedAt   DateTime      @updatedAt

    // Enhanced relationships
    entries ChangelogEntry[]

    // Legacy fields for backward compatibility
    fixes       String[] @default([])
    newFeatures String[] @default([])

    @@index([roadmapId])
    @@index([publishDate])
    @@index([version])
}

model ChangelogEntry {
    id          String             @id @default(uuid())
    changelog   RoadmapChangelog   @relation(fields: [changelogId], references: [id], onDelete: Cascade)
    changelogId String
    type        ChangelogEntryType
    title       String
    description String?

    // Link to actual items (optional)
    issueId   String?
    issue     Issue?   @relation(fields: [issueId], references: [id], onDelete: SetNull)
    featureId String?
    feature   Feature? @relation(fields: [featureId], references: [id], onDelete: SetNull)

    // Metadata
    priority  Importance?
    category  String?
    breaking  Boolean     @default(false)
    createdAt DateTime    @default(now())

    @@index([changelogId])
    @@index([type])
    @@index([issueId])
    @@index([featureId])
}

model FeatureRequest {
    id          String                 @id @default(uuid())
    roadmap     PublicRoadmap          @relation(fields: [roadmapId], references: [id], onDelete: Cascade)
    roadmapId   String
    title       String
    description String
    category    String
    email       String
    name        String?
    ipAddress   String
    status      FeatureRequestStatus
    priority    FeatureRequestPriority
    isPublic    Boolean
    adminNotes  String?
    createdAt   DateTime               @default(now())
    updatedAt   DateTime               @updatedAt

    // Conversion tracking fields
    convertedToFeatureId     String?
    convertedToIssueId       String?
    convertedToRoadmapItemId String?
    convertedAt              DateTime?
    convertedBy              String?
    conversionNotes          String?

    // Relations to converted items
    convertedFeature     Feature?     @relation("FeatureRequestToFeature", fields: [convertedToFeatureId], references: [id], onDelete: SetNull)
    convertedIssue       Issue?       @relation("FeatureRequestToIssue", fields: [convertedToIssueId], references: [id], onDelete: SetNull)
    convertedRoadmapItem RoadmapItem? @relation("FeatureRequestToRoadmapItem", fields: [convertedToRoadmapItemId], references: [id], onDelete: SetNull)

    @@index([roadmapId])
    @@index([email])
    @@index([status])
    @@index([roadmapId, status])
    @@index([convertedToFeatureId])
    @@index([convertedToIssueId])
    @@index([convertedToRoadmapItemId])
}

model Waitlist {
    id               String          @id @default(uuid())
    project          Project         @relation(fields: [projectId], references: [id], onDelete: Cascade)
    projectId        String
    name             String
    slug             String
    description      String
    isPublic         Boolean
    allowNameCapture Boolean
    showPosition     Boolean
    showSocialProof  Boolean
    customMessage    String?
    organization     Organization    @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    organizationId   String
    createdAt        DateTime        @default(now())
    updatedAt        DateTime        @updatedAt
    createdBy        User?           @relation("CreatedWaitlists", fields: [createdById], references: [id], onDelete: SetNull)
    createdById      String?
    entries          WaitlistEntry[]
    referrals        Referral[]

    @@index([projectId])
    @@index([organizationId])
    @@index([slug])
}

model WaitlistEntry {
    id                String    @id @default(uuid())
    waitlist          Waitlist  @relation(fields: [waitlistId], references: [id], onDelete: Cascade)
    waitlistId        String
    email             String
    name              String?
    status            String
    position          Int
    referralCode      String
    referredBy        String?
    verificationToken String?
    verifiedAt        DateTime?
    invitedAt         DateTime?
    joinedAt          DateTime?
    ipAddress         String
    userAgent         String?
    utmSource         String?
    utmMedium         String?
    utmCampaign       String?
    createdAt         DateTime  @default(now())
    updatedAt         DateTime  @updatedAt

    // Relations
    referrals Referral[]

    @@index([waitlistId])
    @@index([email])
    @@index([referralCode])
    @@index([referredBy])
    @@index([status])
    @@index([waitlistId, status])
    @@index([waitlistId, position])
    @@index([verificationToken])
}

model Feature {
    id              String       @id @default(uuid())
    name            String
    description     String
    projectId       String
    phase           FeaturePhase
    businessValue   Float?
    estimatedEffort Float?
    startDate       DateTime?
    endDate         DateTime?
    priority        Importance
    assignedToId    String?
    parentFeatureId String?
    organizationId  String
    createdAt       DateTime     @default(now())
    updatedAt       DateTime     @updatedAt

    // Relations
    parentFeature Feature?     @relation("FeatureHierarchy", fields: [parentFeatureId], references: [id], onDelete: SetNull)
    subFeatures   Feature[]    @relation("FeatureHierarchy")
    organization  Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    assignedTo    User?        @relation(fields: [assignedToId], references: [id], onDelete: SetNull)
    project       Project      @relation(fields: [projectId], references: [id], onDelete: Cascade)
    milestone     Milestone?   @relation(fields: [milestoneId], references: [id], onDelete: SetNull)
    milestoneId   String?

    // Feature dependencies
    dependencies FeatureDependency[] @relation("Dependants")
    dependentOn  FeatureDependency[] @relation("Dependencies")
    FeatureLink  FeatureLink[]

    // Feedback conversion
    convertedFromFeedback       RoadmapFeedback[] @relation("FeedbackToFeature")
    convertedFromFeatureRequest FeatureRequest[]  @relation("FeatureRequestToFeature")

    // Changelog entries
    changelogEntries ChangelogEntry[]

    @@index([projectId])
    @@index([milestoneId])
    @@map("feature")
}

model FeatureDependency {
    id             String   @id @default(uuid())
    organizationId String
    featureId      String
    dependencyId   String
    createdAt      DateTime @default(now())

    // Relations
    organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    feature      Feature      @relation("Dependants", fields: [featureId], references: [id], onDelete: Cascade)
    dependency   Feature      @relation("Dependencies", fields: [dependencyId], references: [id], onDelete: Cascade)

    @@index([organizationId])
    @@index([featureId])
    @@index([dependencyId])
    @@index([organizationId, featureId])
    @@index([organizationId, dependencyId])
    @@map("featureDependency")
}

model FeatureLink {
    id             String       @id @default(uuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    featureId      String
    feature        Feature      @relation(fields: [featureId], references: [id], onDelete: Cascade)
    url            String
    createdAt      DateTime     @default(now())

    @@index([organizationId])
    @@index([featureId])
    @@index([organizationId, featureId])
    @@map("featureLink")
}

model Milestone {
    id          String          @id @default(uuid())
    name        String
    description String?
    status      MilestoneStatus @default(NOT_STARTED)
    startDate   DateTime?
    endDate     DateTime?
    createdAt   DateTime        @default(now())
    updatedAt   DateTime        @updatedAt

    // Relations
    project        Project      @relation(fields: [projectId], references: [id], onDelete: Cascade)
    projectId      String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    organizationId String
    owner          User?        @relation("MilestoneOwner", fields: [ownerId], references: [id], onDelete: SetNull)
    ownerId        String?

    // Related items
    issues   Issue[]
    features Feature[]

    // Dependencies
    dependsOn MilestoneDependency[] @relation("Dependants")
    blocking  MilestoneDependency[] @relation("Dependencies")

    @@index([projectId])
    @@index([organizationId])
    @@index([ownerId])
    @@map("milestone")
}

model MilestoneDependency {
    id             String   @id @default(uuid())
    organizationId String
    milestoneId    String
    dependencyId   String
    createdAt      DateTime @default(now())

    // Relations
    organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    milestone    Milestone    @relation("Dependants", fields: [milestoneId], references: [id], onDelete: Cascade)
    dependency   Milestone    @relation("Dependencies", fields: [dependencyId], references: [id], onDelete: Cascade)

    @@index([organizationId])
    @@index([milestoneId])
    @@index([dependencyId])
    @@index([organizationId, milestoneId])
    @@index([organizationId, dependencyId])
    @@map("milestoneDependency")
}

model Referral {
    id             String        @id @default(uuid())
    referrer       WaitlistEntry @relation(fields: [referrerId], references: [id], onDelete: Cascade)
    referrerId     String
    referredEmail  String
    referredName   String?
    ipAddress      String
    userAgent      String?
    referrerCode   String
    waitlistId     String
    waitlist       Waitlist      @relation(fields: [waitlistId], references: [id], onDelete: Cascade)
    organizationId String
    organization   Organization  @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    createdAt      DateTime      @default(now())

    @@index([referrerId])
    @@index([waitlistId])
    @@index([organizationId])
    @@index([referredEmail])
    @@index([referrerCode])
    @@index([createdAt])
    @@map("referral")
}

model AssetView {
    id             String       @id @default(uuid())
    asset          Asset        @relation(fields: [assetId], references: [id], onDelete: Cascade)
    assetId        String
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    userId         String?
    user           User?        @relation(fields: [userId], references: [id], onDelete: SetNull)
    ipAddress      String
    userAgent      String?
    referrer       String?
    viewedAt       DateTime     @default(now())

    @@index([assetId])
    @@index([organizationId])
    @@index([userId])
    @@index([viewedAt])
    @@index([assetId, viewedAt])
    @@map("asset_view")
}

model AssetDownload {
    id             String       @id @default(uuid())
    asset          Asset        @relation(fields: [assetId], references: [id], onDelete: Cascade)
    assetId        String
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    userId         String?
    user           User?        @relation(fields: [userId], references: [id], onDelete: SetNull)
    ipAddress      String
    userAgent      String?
    referrer       String?
    downloadedAt   DateTime     @default(now())

    @@index([assetId])
    @@index([organizationId])
    @@index([userId])
    @@index([downloadedAt])
    @@index([assetId, downloadedAt])
    @@map("asset_download")
}
