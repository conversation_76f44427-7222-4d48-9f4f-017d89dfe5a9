
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.14.0
 * Query Engine version: 717184b7b35ea05dfa71a3236b7af656013e1e49
 */
Prisma.prismaVersion = {
  client: "6.14.0",
  engine: "717184b7b35ea05dfa71a3236b7af656013e1e49"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.IdeaScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  industry: 'industry',
  ownerId: 'ownerId',
  organizationId: 'organizationId',
  internal: 'internal',
  openSource: 'openSource',
  status: 'status',
  aiOverallValidation: 'aiOverallValidation',
  problemSolved: 'problemSolved',
  solutionOffered: 'solutionOffered',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CompetitorScalarFieldEnum = {
  id: 'id',
  ideaId: 'ideaId',
  name: 'name',
  website: 'website',
  description: 'description',
  logoUrl: 'logoUrl',
  marketShare: 'marketShare',
  annualRevenue: 'annualRevenue',
  employeeCount: 'employeeCount',
  foundedYear: 'foundedYear',
  headquarters: 'headquarters',
  targetAudience: 'targetAudience',
  threatLevel: 'threatLevel',
  userGrowthRate: 'userGrowthRate',
  churnRate: 'churnRate',
  customerSatisfaction: 'customerSatisfaction',
  marketCap: 'marketCap',
  lastUpdated: 'lastUpdated',
  createdAt: 'createdAt',
  isActive: 'isActive'
};

exports.Prisma.CompetitiveMoveScalarFieldEnum = {
  id: 'id',
  competitorId: 'competitorId',
  moveType: 'moveType',
  title: 'title',
  description: 'description',
  impactLevel: 'impactLevel',
  targetAudience: 'targetAudience',
  affectedFeatures: 'affectedFeatures',
  announcedDate: 'announcedDate',
  launchDate: 'launchDate',
  completionDate: 'completionDate',
  userFeedback: 'userFeedback',
  pressCoverage: 'pressCoverage',
  opportunities: 'opportunities',
  threats: 'threats',
  responseRequired: 'responseRequired',
  responseStrategy: 'responseStrategy',
  createdAt: 'createdAt'
};

exports.Prisma.CompetitorSwotScalarFieldEnum = {
  id: 'id',
  impact: 'impact',
  type: 'type',
  swotAnalysis: 'swotAnalysis',
  competitorId: 'competitorId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProjectScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  platform: 'platform',
  ai: 'ai',
  orm: 'orm',
  database: 'database',
  auth: 'auth',
  framework: 'framework',
  infrastructure: 'infrastructure',
  dueDate: 'dueDate',
  status: 'status',
  ideaId: 'ideaId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  organizationId: 'organizationId',
  createdById: 'createdById'
};

exports.Prisma.IssueScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  organizationId: 'organizationId',
  projectId: 'projectId',
  milestoneId: 'milestoneId',
  featureId: 'featureId',
  parentIssueId: 'parentIssueId',
  status: 'status',
  priority: 'priority',
  label: 'label',
  dueDate: 'dueDate',
  assignedToId: 'assignedToId',
  achieved: 'achieved',
  isPublic: 'isPublic',
  sourceType: 'sourceType',
  sourceFeedbackId: 'sourceFeedbackId'
};

exports.Prisma.IssueDependencyScalarFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  issueId: 'issueId',
  dependencyId: 'dependencyId',
  createdAt: 'createdAt'
};

exports.Prisma.IssueLinkScalarFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  issueId: 'issueId',
  url: 'url',
  createdAt: 'createdAt'
};

exports.Prisma.AssetScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  type: 'type',
  projectId: 'projectId',
  organizationId: 'organizationId',
  storageId: 'storageId',
  fileName: 'fileName',
  fileSize: 'fileSize',
  mimeType: 'mimeType',
  url: 'url',
  linkType: 'linkType',
  tags: 'tags',
  category: 'category',
  thumbnailUrl: 'thumbnailUrl',
  isPublic: 'isPublic',
  uploadedById: 'uploadedById',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ActivityFeedScalarFieldEnum = {
  id: 'id',
  type: 'type',
  title: 'title',
  description: 'description',
  entityType: 'entityType',
  entityId: 'entityId',
  organizationId: 'organizationId',
  userId: 'userId',
  oldValue: 'oldValue',
  newValue: 'newValue',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PublicRoadmapScalarFieldEnum = {
  id: 'id',
  projectId: 'projectId',
  name: 'name',
  slug: 'slug',
  description: 'description',
  isPublic: 'isPublic',
  allowVoting: 'allowVoting',
  allowFeedback: 'allowFeedback',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RoadmapItemScalarFieldEnum = {
  id: 'id',
  roadmapId: 'roadmapId',
  title: 'title',
  description: 'description',
  status: 'status',
  category: 'category',
  isPublic: 'isPublic',
  priority: 'priority',
  targetDate: 'targetDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RoadmapVoteScalarFieldEnum = {
  id: 'id',
  roadmapItemId: 'roadmapItemId',
  userId: 'userId',
  ipAddress: 'ipAddress',
  createdAt: 'createdAt'
};

exports.Prisma.RoadmapFeedbackScalarFieldEnum = {
  id: 'id',
  roadmapItemId: 'roadmapItemId',
  userId: 'userId',
  ipAddress: 'ipAddress',
  content: 'content',
  sentiment: 'sentiment',
  isApproved: 'isApproved',
  convertedToFeatureId: 'convertedToFeatureId',
  convertedToIssueId: 'convertedToIssueId',
  convertedAt: 'convertedAt',
  convertedBy: 'convertedBy',
  conversionNotes: 'conversionNotes',
  createdAt: 'createdAt'
};

exports.Prisma.RoadmapChangelogScalarFieldEnum = {
  id: 'id',
  roadmapId: 'roadmapId',
  title: 'title',
  description: 'description',
  version: 'version',
  publishDate: 'publishDate',
  isPublished: 'isPublished',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  fixes: 'fixes',
  newFeatures: 'newFeatures'
};

exports.Prisma.ChangelogEntryScalarFieldEnum = {
  id: 'id',
  changelogId: 'changelogId',
  type: 'type',
  title: 'title',
  description: 'description',
  issueId: 'issueId',
  featureId: 'featureId',
  priority: 'priority',
  category: 'category',
  breaking: 'breaking',
  createdAt: 'createdAt'
};

exports.Prisma.FeatureRequestScalarFieldEnum = {
  id: 'id',
  roadmapId: 'roadmapId',
  title: 'title',
  description: 'description',
  category: 'category',
  email: 'email',
  name: 'name',
  ipAddress: 'ipAddress',
  status: 'status',
  priority: 'priority',
  isPublic: 'isPublic',
  adminNotes: 'adminNotes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  convertedToFeatureId: 'convertedToFeatureId',
  convertedToIssueId: 'convertedToIssueId',
  convertedToRoadmapItemId: 'convertedToRoadmapItemId',
  convertedAt: 'convertedAt',
  convertedBy: 'convertedBy',
  conversionNotes: 'conversionNotes'
};

exports.Prisma.WaitlistScalarFieldEnum = {
  id: 'id',
  projectId: 'projectId',
  name: 'name',
  slug: 'slug',
  description: 'description',
  isPublic: 'isPublic',
  allowNameCapture: 'allowNameCapture',
  showPosition: 'showPosition',
  showSocialProof: 'showSocialProof',
  customMessage: 'customMessage',
  organizationId: 'organizationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdById: 'createdById'
};

exports.Prisma.WaitlistEntryScalarFieldEnum = {
  id: 'id',
  waitlistId: 'waitlistId',
  email: 'email',
  name: 'name',
  status: 'status',
  position: 'position',
  referralCode: 'referralCode',
  referredBy: 'referredBy',
  verificationToken: 'verificationToken',
  verifiedAt: 'verifiedAt',
  invitedAt: 'invitedAt',
  joinedAt: 'joinedAt',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  utmSource: 'utmSource',
  utmMedium: 'utmMedium',
  utmCampaign: 'utmCampaign',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FeatureScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  projectId: 'projectId',
  phase: 'phase',
  businessValue: 'businessValue',
  estimatedEffort: 'estimatedEffort',
  startDate: 'startDate',
  endDate: 'endDate',
  priority: 'priority',
  assignedToId: 'assignedToId',
  parentFeatureId: 'parentFeatureId',
  organizationId: 'organizationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  milestoneId: 'milestoneId'
};

exports.Prisma.FeatureDependencyScalarFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  featureId: 'featureId',
  dependencyId: 'dependencyId',
  createdAt: 'createdAt'
};

exports.Prisma.FeatureLinkScalarFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  featureId: 'featureId',
  url: 'url',
  createdAt: 'createdAt'
};

exports.Prisma.MilestoneScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  status: 'status',
  startDate: 'startDate',
  endDate: 'endDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  projectId: 'projectId',
  organizationId: 'organizationId',
  ownerId: 'ownerId'
};

exports.Prisma.MilestoneDependencyScalarFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  milestoneId: 'milestoneId',
  dependencyId: 'dependencyId',
  createdAt: 'createdAt'
};

exports.Prisma.ReferralScalarFieldEnum = {
  id: 'id',
  referrerId: 'referrerId',
  referredEmail: 'referredEmail',
  referredName: 'referredName',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  referrerCode: 'referrerCode',
  waitlistId: 'waitlistId',
  organizationId: 'organizationId',
  createdAt: 'createdAt'
};

exports.Prisma.AssetViewScalarFieldEnum = {
  id: 'id',
  assetId: 'assetId',
  organizationId: 'organizationId',
  userId: 'userId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  referrer: 'referrer',
  viewedAt: 'viewedAt'
};

exports.Prisma.AssetDownloadScalarFieldEnum = {
  id: 'id',
  assetId: 'assetId',
  organizationId: 'organizationId',
  userId: 'userId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  referrer: 'referrer',
  downloadedAt: 'downloadedAt'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  emailVerified: 'emailVerified',
  image: 'image',
  role: 'role',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  twoFactorEnabled: 'twoFactorEnabled'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  expiresAt: 'expiresAt',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  userId: 'userId',
  activeOrganizationId: 'activeOrganizationId',
  token: 'token',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AccountScalarFieldEnum = {
  id: 'id',
  accountId: 'accountId',
  providerId: 'providerId',
  userId: 'userId',
  accessToken: 'accessToken',
  refreshToken: 'refreshToken',
  idToken: 'idToken',
  expiresAt: 'expiresAt',
  password: 'password',
  accessTokenExpiresAt: 'accessTokenExpiresAt',
  refreshTokenExpiresAt: 'refreshTokenExpiresAt',
  scope: 'scope',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.VerificationScalarFieldEnum = {
  id: 'id',
  identifier: 'identifier',
  value: 'value',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrganizationScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  logo: 'logo',
  createdAt: 'createdAt',
  metadata: 'metadata'
};

exports.Prisma.MemberScalarFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  userId: 'userId',
  role: 'role',
  createdAt: 'createdAt'
};

exports.Prisma.InvitationScalarFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  email: 'email',
  role: 'role',
  status: 'status',
  expiresAt: 'expiresAt',
  inviterId: 'inviterId'
};

exports.Prisma.PasskeyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  publicKey: 'publicKey',
  userId: 'userId',
  webauthnUserID: 'webauthnUserID',
  counter: 'counter',
  deviceType: 'deviceType',
  backedUp: 'backedUp',
  transports: 'transports',
  createdAt: 'createdAt',
  credentialID: 'credentialID'
};

exports.Prisma.TwoFactorScalarFieldEnum = {
  id: 'id',
  secret: 'secret',
  backupCodes: 'backupCodes',
  userId: 'userId'
};

exports.Prisma.SubscriptionScalarFieldEnum = {
  id: 'id',
  status: 'status',
  organisation_id: 'organisation_id',
  subscription_id: 'subscription_id',
  product_id: 'product_id',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.IntegrationScalarFieldEnum = {
  id: 'id',
  name: 'name',
  type: 'type',
  config: 'config',
  isActive: 'isActive',
  organizationId: 'organizationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  createdById: 'createdById'
};

exports.Prisma.IntegrationUsageScalarFieldEnum = {
  id: 'id',
  integrationId: 'integrationId',
  entityType: 'entityType',
  entityId: 'entityId',
  purpose: 'purpose',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ApiKeyScalarFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  name: 'name',
  keyHash: 'keyHash',
  keyPreview: 'keyPreview',
  permissions: 'permissions',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  lastUsed: 'lastUsed',
  isActive: 'isActive',
  expiresAt: 'expiresAt'
};

exports.Prisma.ApiCallScalarFieldEnum = {
  id: 'id',
  organizationId: 'organizationId',
  apiKeyId: 'apiKeyId',
  endpoint: 'endpoint',
  method: 'method',
  statusCode: 'statusCode',
  responseTime: 'responseTime',
  userAgent: 'userAgent',
  ipAddress: 'ipAddress',
  createdAt: 'createdAt'
};

exports.Prisma.IdeaValidationScalarFieldEnum = {
  id: 'id',
  ideaId: 'ideaId',
  overallScore: 'overallScore',
  overallStatus: 'overallStatus',
  confidenceLevel: 'confidenceLevel',
  validationProgress: 'validationProgress',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  lastUpdatedAt: 'lastUpdatedAt',
  version: 'version',
  parentValidationId: 'parentValidationId',
  isLatest: 'isLatest',
  revalidationReason: 'revalidationReason',
  dataSourcesUpdated: 'dataSourcesUpdated',
  lastDataRefresh: 'lastDataRefresh',
  nextRevalidationDue: 'nextRevalidationDue'
};

exports.Prisma.ValidationMetricsScalarFieldEnum = {
  id: 'id',
  validationId: 'validationId',
  overallStrengthScore: 'overallStrengthScore',
  overallRiskScore: 'overallRiskScore',
  timeToMarket: 'timeToMarket',
  fundingRequired: 'fundingRequired',
  breakEvenMonth: 'breakEvenMonth',
  customerPayback: 'customerPayback',
  marketPenetration: 'marketPenetration',
  immediateActions: 'immediateActions',
  shortTermActions: 'shortTermActions',
  longTermActions: 'longTermActions',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MarketValidationScalarFieldEnum = {
  id: 'id',
  validationId: 'validationId',
  totalAddressableMarket: 'totalAddressableMarket',
  serviceableAddressableMarket: 'serviceableAddressableMarket',
  serviceableObtainableMarket: 'serviceableObtainableMarket',
  marketGrowthRate: 'marketGrowthRate',
  primaryCustomerSegment: 'primaryCustomerSegment',
  customerInterviews: 'customerInterviews',
  surveyResponses: 'surveyResponses',
  overallMarketScore: 'overallMarketScore',
  primaryRegions: 'primaryRegions',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MarketInsightScalarFieldEnum = {
  id: 'id',
  marketValidationId: 'marketValidationId',
  category: 'category',
  impact: 'impact',
  urgency: 'urgency',
  label: 'label',
  description: 'description',
  createdAt: 'createdAt'
};

exports.Prisma.MarketRegionScoreScalarFieldEnum = {
  id: 'id',
  marketValidationId: 'marketValidationId',
  region: 'region',
  score: 'score',
  createdAt: 'createdAt'
};

exports.Prisma.BusinessValidationScalarFieldEnum = {
  id: 'id',
  validationId: 'validationId',
  primaryRevenueModel: 'primaryRevenueModel',
  pricingStrategy: 'pricingStrategy',
  pricePoint: 'pricePoint',
  customerAcquisitionCost: 'customerAcquisitionCost',
  customerLifetimeValue: 'customerLifetimeValue',
  monthlyChurnRate: 'monthlyChurnRate',
  breakEvenMonth: 'breakEvenMonth',
  initialInvestment: 'initialInvestment',
  totalFundingNeeded: 'totalFundingNeeded',
  goToMarketStrategy: 'goToMarketStrategy',
  salesCycleLength: 'salesCycleLength',
  overallBusinessScore: 'overallBusinessScore',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BusinessInsightScalarFieldEnum = {
  id: 'id',
  businessValidationId: 'businessValidationId',
  category: 'category',
  impact: 'impact',
  urgency: 'urgency',
  cost: 'cost',
  label: 'label',
  description: 'description',
  createdAt: 'createdAt'
};

exports.Prisma.RiskAnalysisScalarFieldEnum = {
  id: 'id',
  validationId: 'validationId',
  overallRiskScore: 'overallRiskScore',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RiskItemScalarFieldEnum = {
  id: 'id',
  riskAnalysisId: 'riskAnalysisId',
  category: 'category',
  description: 'description',
  impact: 'impact',
  probability: 'probability',
  mitigation: 'mitigation',
  createdAt: 'createdAt'
};

exports.Prisma.ProductMarketFitAnalysisScalarFieldEnum = {
  id: 'id',
  validationId: 'validationId',
  pmfScore: 'pmfScore',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PMFMetricScalarFieldEnum = {
  id: 'id',
  productMarketFitAnalysisId: 'productMarketFitAnalysisId',
  name: 'name',
  value: 'value',
  unit: 'unit',
  trend: 'trend',
  benchmark: 'benchmark',
  createdAt: 'createdAt'
};

exports.Prisma.PMFFeedbackScalarFieldEnum = {
  id: 'id',
  productMarketFitAnalysisId: 'productMarketFitAnalysisId',
  source: 'source',
  sentiment: 'sentiment',
  content: 'content',
  tags: 'tags',
  createdAt: 'createdAt'
};

exports.Prisma.MonthlyProjectionScalarFieldEnum = {
  id: 'id',
  businessValidationId: 'businessValidationId',
  month: 'month',
  revenue: 'revenue',
  costs: 'costs',
  users: 'users',
  createdAt: 'createdAt'
};

exports.Prisma.AcquisitionChannelScalarFieldEnum = {
  id: 'id',
  businessValidationId: 'businessValidationId',
  channel: 'channel',
  effectiveness: 'effectiveness',
  cost: 'cost',
  createdAt: 'createdAt'
};

exports.Prisma.PricingTierScalarFieldEnum = {
  id: 'id',
  pricingStrategyAnalysisId: 'pricingStrategyAnalysisId',
  tierName: 'tierName',
  tierPrice: 'tierPrice',
  tierFeatures: 'tierFeatures',
  targetSegment: 'targetSegment',
  conversionRate: 'conversionRate',
  popularityScore: 'popularityScore',
  profitMargin: 'profitMargin',
  competitiveScore: 'competitiveScore',
  createdAt: 'createdAt'
};

exports.Prisma.CompetitorPricingScalarFieldEnum = {
  id: 'id',
  pricingStrategyAnalysisId: 'pricingStrategyAnalysisId',
  competitorName: 'competitorName',
  pricingModel: 'pricingModel',
  basePrice: 'basePrice',
  premiumPrice: 'premiumPrice',
  featureComparison: 'featureComparison',
  valueComparison: 'valueComparison',
  marketPosition: 'marketPosition',
  marketShare: 'marketShare',
  customerSatisfaction: 'customerSatisfaction',
  pricingAdvantage: 'pricingAdvantage',
  createdAt: 'createdAt'
};

exports.Prisma.CustomerJourneyMappingScalarFieldEnum = {
  id: 'id',
  validationId: 'validationId',
  totalJourneyStages: 'totalJourneyStages',
  averageJourneyTime: 'averageJourneyTime',
  overallJourneyScore: 'overallJourneyScore',
  conversionRate: 'conversionRate',
  dropOffRate: 'dropOffRate',
  customerSatisfaction: 'customerSatisfaction',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.JourneyStageScalarFieldEnum = {
  id: 'id',
  customerJourneyMappingId: 'customerJourneyMappingId',
  stageName: 'stageName',
  stageOrder: 'stageOrder',
  averageDuration: 'averageDuration',
  conversionRate: 'conversionRate',
  satisfactionScore: 'satisfactionScore',
  frictionScore: 'frictionScore',
  emotionalState: 'emotionalState',
  customerGoals: 'customerGoals',
  customerActions: 'customerActions',
  customerThoughts: 'customerThoughts',
  customerEmotions: 'customerEmotions',
  dropOffRate: 'dropOffRate',
  supportTickets: 'supportTickets',
  timeToComplete: 'timeToComplete',
  createdAt: 'createdAt'
};

exports.Prisma.TouchpointScalarFieldEnum = {
  id: 'id',
  customerJourneyMappingId: 'customerJourneyMappingId',
  touchpointName: 'touchpointName',
  touchpointType: 'touchpointType',
  channel: 'channel',
  stageInJourney: 'stageInJourney',
  effectivenessScore: 'effectivenessScore',
  satisfactionScore: 'satisfactionScore',
  usageFrequency: 'usageFrequency',
  importanceScore: 'importanceScore',
  optimizationPotential: 'optimizationPotential',
  costEfficiency: 'costEfficiency',
  automationPotential: 'automationPotential',
  customerExpectations: 'customerExpectations',
  currentExperience: 'currentExperience',
  improvementAreas: 'improvementAreas',
  createdAt: 'createdAt'
};

exports.Prisma.JourneyPainPointScalarFieldEnum = {
  id: 'id',
  customerJourneyMappingId: 'customerJourneyMappingId',
  painPointName: 'painPointName',
  journeyStage: 'journeyStage',
  painCategory: 'painCategory',
  severityScore: 'severityScore',
  frequencyScore: 'frequencyScore',
  impactScore: 'impactScore',
  resolutionDifficulty: 'resolutionDifficulty',
  dropOffIncrease: 'dropOffIncrease',
  supportCost: 'supportCost',
  revenueImpact: 'revenueImpact',
  currentMitigation: 'currentMitigation',
  proposedSolution: 'proposedSolution',
  solutionPriority: 'solutionPriority',
  createdAt: 'createdAt'
};

exports.Prisma.TargetAudienceSegmentationScalarFieldEnum = {
  id: 'id',
  validationId: 'validationId',
  primarySegment: 'primarySegment',
  totalSegments: 'totalSegments',
  totalMarketSize: 'totalMarketSize',
  overallSegmentationScore: 'overallSegmentationScore',
  averageSegmentSize: 'averageSegmentSize',
  segmentAccessibility: 'segmentAccessibility',
  marketPenetration: 'marketPenetration',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AudienceSegmentScalarFieldEnum = {
  id: 'id',
  targetAudienceSegmentationId: 'targetAudienceSegmentationId',
  segmentName: 'segmentName',
  segmentSize: 'segmentSize',
  attractivenessScore: 'attractivenessScore',
  accessibilityScore: 'accessibilityScore',
  profitabilityScore: 'profitabilityScore',
  primaryNeed: 'primaryNeed',
  secondaryNeeds: 'secondaryNeeds',
  preferredSolution: 'preferredSolution',
  budgetRange: 'budgetRange',
  createdAt: 'createdAt'
};

exports.Prisma.MarketTrendAnalysisScalarFieldEnum = {
  id: 'id',
  validationId: 'validationId',
  primaryTrend: 'primaryTrend',
  totalTrendsTracked: 'totalTrendsTracked',
  analysisTimeframe: 'analysisTimeframe',
  overallTrendScore: 'overallTrendScore',
  trendStrength: 'trendStrength',
  marketGrowthRate: 'marketGrowthRate',
  adoptionRate: 'adoptionRate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MarketTrendScalarFieldEnum = {
  id: 'id',
  marketTrendAnalysisId: 'marketTrendAnalysisId',
  trendName: 'trendName',
  trendCategory: 'trendCategory',
  impactScore: 'impactScore',
  timelineMonths: 'timelineMonths',
  certaintyLevel: 'certaintyLevel',
  opportunityScore: 'opportunityScore',
  threatScore: 'threatScore',
  description: 'description',
  createdAt: 'createdAt'
};

exports.Prisma.CustomerNeedAnalysisScalarFieldEnum = {
  id: 'id',
  validationId: 'validationId',
  primaryNeed: 'primaryNeed',
  totalNeedsIdentified: 'totalNeedsIdentified',
  totalPainPoints: 'totalPainPoints',
  overallNeedScore: 'overallNeedScore',
  needUrgency: 'needUrgency',
  solutionGap: 'solutionGap',
  customerWillingness: 'customerWillingness',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CustomerNeedScalarFieldEnum = {
  id: 'id',
  customerNeedAnalysisId: 'customerNeedAnalysisId',
  needName: 'needName',
  needCategory: 'needCategory',
  intensityScore: 'intensityScore',
  frequencyScore: 'frequencyScore',
  urgencyScore: 'urgencyScore',
  satisfactionGap: 'satisfactionGap',
  triggerEvents: 'triggerEvents',
  desiredOutcome: 'desiredOutcome',
  currentSolution: 'currentSolution',
  createdAt: 'createdAt'
};

exports.Prisma.PainPointScalarFieldEnum = {
  id: 'id',
  customerNeedAnalysisId: 'customerNeedAnalysisId',
  painName: 'painName',
  painCategory: 'painCategory',
  severityScore: 'severityScore',
  frequencyScore: 'frequencyScore',
  impactScore: 'impactScore',
  emotionalToll: 'emotionalToll',
  timeCostHours: 'timeCostHours',
  financialCost: 'financialCost',
  opportunityCost: 'opportunityCost',
  painTriggers: 'painTriggers',
  currentMitigation: 'currentMitigation',
  createdAt: 'createdAt'
};

exports.Prisma.PricingStrategyAnalysisScalarFieldEnum = {
  id: 'id',
  validationId: 'validationId',
  primaryStrategy: 'primaryStrategy',
  recommendedPrice: 'recommendedPrice',
  totalTiersAnalyzed: 'totalTiersAnalyzed',
  overallPricingScore: 'overallPricingScore',
  priceAcceptance: 'priceAcceptance',
  competitivenessScore: 'competitivenessScore',
  profitabilityScore: 'profitabilityScore',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.IdeaStatus = exports.$Enums.IdeaStatus = {
  INVALIDATED: 'INVALIDATED',
  VALIDATED: 'VALIDATED',
  FAILED: 'FAILED',
  IN_PROGRESS: 'IN_PROGRESS',
  LAUNCHED: 'LAUNCHED'
};

exports.Importance = exports.$Enums.Importance = {
  CRITICAL: 'CRITICAL',
  HIGH: 'HIGH',
  MEDIUM: 'MEDIUM',
  LOW: 'LOW'
};

exports.SwotType = exports.$Enums.SwotType = {
  Strength: 'Strength',
  Weakness: 'Weakness',
  Opportunity: 'Opportunity',
  Threat: 'Threat'
};

exports.ProjectPlatform = exports.$Enums.ProjectPlatform = {
  web: 'web',
  mobile: 'mobile',
  both: 'both',
  api: 'api',
  plugin: 'plugin',
  desktop: 'desktop',
  cli: 'cli'
};

exports.ProjectStatus = exports.$Enums.ProjectStatus = {
  planning: 'planning',
  in_progress: 'in_progress',
  review: 'review',
  completed: 'completed'
};

exports.IssueStatus = exports.$Enums.IssueStatus = {
  BACKLOG: 'BACKLOG',
  IN_PROGRESS: 'IN_PROGRESS',
  IN_REVIEW: 'IN_REVIEW',
  DONE: 'DONE',
  BLOCKED: 'BLOCKED',
  CANCELLED: 'CANCELLED'
};

exports.IssueLabel = exports.$Enums.IssueLabel = {
  UI: 'UI',
  BUG: 'BUG',
  FEATURE: 'FEATURE',
  IMPROVEMENT: 'IMPROVEMENT',
  TASK: 'TASK',
  DOCUMENTATION: 'DOCUMENTATION',
  REFACTOR: 'REFACTOR',
  PERFORMANCE: 'PERFORMANCE',
  DESIGN: 'DESIGN',
  SECURITY: 'SECURITY',
  ACCESSIBILITY: 'ACCESSIBILITY',
  TESTING: 'TESTING',
  INTERNATIONALIZATION: 'INTERNATIONALIZATION'
};

exports.AssetType = exports.$Enums.AssetType = {
  image: 'image',
  document: 'document',
  video: 'video',
  link: 'link',
  code: 'code',
  design: 'design',
  other: 'other'
};

exports.LinkType = exports.$Enums.LinkType = {
  youtube: 'youtube',
  figma: 'figma',
  notion: 'notion',
  github: 'github',
  dribbble: 'dribbble',
  behance: 'behance',
  external: 'external'
};

exports.AssetCategory = exports.$Enums.AssetCategory = {
  branding: 'branding',
  ui_design: 'ui_design',
  mockups: 'mockups',
  documentation: 'documentation',
  inspiration: 'inspiration',
  code_snippets: 'code_snippets',
  presentations: 'presentations',
  tutorials: 'tutorials',
  other: 'other'
};

exports.ActivityType = exports.$Enums.ActivityType = {
  CREATED: 'CREATED',
  UPDATED: 'UPDATED',
  PHASE_CHANGED: 'PHASE_CHANGED',
  ASSIGNED: 'ASSIGNED',
  UNASSIGNED: 'UNASSIGNED',
  DEPENDENCY_ADDED: 'DEPENDENCY_ADDED',
  DEPENDENCY_REMOVED: 'DEPENDENCY_REMOVED',
  LINK_ADDED: 'LINK_ADDED',
  LINK_REMOVED: 'LINK_REMOVED',
  PARENT_CHANGED: 'PARENT_CHANGED'
};

exports.EntityType = exports.$Enums.EntityType = {
  PROJECT: 'PROJECT',
  FEATURE: 'FEATURE',
  ISSUE: 'ISSUE',
  IDEA: 'IDEA',
  ROADMAP: 'ROADMAP',
  MILESTONE: 'MILESTONE'
};

exports.RoadmapFeedbackSentiment = exports.$Enums.RoadmapFeedbackSentiment = {
  positive: 'positive',
  neutral: 'neutral',
  negative: 'negative'
};

exports.ChangelogEntryType = exports.$Enums.ChangelogEntryType = {
  FEATURE: 'FEATURE',
  FIX: 'FIX',
  IMPROVEMENT: 'IMPROVEMENT',
  BREAKING: 'BREAKING',
  SECURITY: 'SECURITY',
  DEPRECATION: 'DEPRECATION',
  DOCUMENTATION: 'DOCUMENTATION',
  PERFORMANCE: 'PERFORMANCE'
};

exports.FeatureRequestStatus = exports.$Enums.FeatureRequestStatus = {
  pending: 'pending',
  under_review: 'under_review',
  approved: 'approved',
  rejected: 'rejected',
  implemented: 'implemented'
};

exports.FeatureRequestPriority = exports.$Enums.FeatureRequestPriority = {
  low: 'low',
  medium: 'medium',
  high: 'high',
  urgent: 'urgent'
};

exports.FeaturePhase = exports.$Enums.FeaturePhase = {
  DISCOVERY: 'DISCOVERY',
  PLANNING: 'PLANNING',
  DEVELOPMENT: 'DEVELOPMENT',
  TESTING: 'TESTING',
  DEPLOYMENT: 'DEPLOYMENT',
  COMPLETED: 'COMPLETED',
  RELEASE: 'RELEASE',
  LIVE: 'LIVE',
  DEPRECATED: 'DEPRECATED'
};

exports.MilestoneStatus = exports.$Enums.MilestoneStatus = {
  NOT_STARTED: 'NOT_STARTED',
  IN_PROGRESS: 'IN_PROGRESS',
  AT_RISK: 'AT_RISK',
  COMPLETED: 'COMPLETED',
  DELAYED: 'DELAYED'
};

exports.IntegrationType = exports.$Enums.IntegrationType = {
  RESEND: 'RESEND',
  LOOPS: 'LOOPS',
  SENDGRID: 'SENDGRID',
  MAILCHIMP: 'MAILCHIMP',
  CONVERTKIT: 'CONVERTKIT',
  GITHUB: 'GITHUB'
};

exports.ApiPermission = exports.$Enums.ApiPermission = {
  READ: 'READ',
  WRITE: 'WRITE',
  DELETE: 'DELETE',
  ADMIN: 'ADMIN'
};

exports.ValidationStatus = exports.$Enums.ValidationStatus = {
  PENDING: 'PENDING',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  REQUIRES_REVIEW: 'REQUIRES_REVIEW'
};

exports.CustomerSegment = exports.$Enums.CustomerSegment = {
  B2B_ENTERPRISE: 'B2B_ENTERPRISE',
  B2B_SMB: 'B2B_SMB',
  B2C_CONSUMER: 'B2C_CONSUMER',
  B2C_PROSUMER: 'B2C_PROSUMER',
  B2G_GOVERNMENT: 'B2G_GOVERNMENT',
  MARKETPLACE: 'MARKETPLACE',
  PLATFORM: 'PLATFORM'
};

exports.RevenueModel = exports.$Enums.RevenueModel = {
  SUBSCRIPTION: 'SUBSCRIPTION',
  FREEMIUM: 'FREEMIUM',
  ONE_TIME_PURCHASE: 'ONE_TIME_PURCHASE',
  TRANSACTION_FEE: 'TRANSACTION_FEE',
  ADVERTISING: 'ADVERTISING',
  COMMISSION: 'COMMISSION',
  LICENSING: 'LICENSING',
  USAGE_BASED: 'USAGE_BASED',
  HYBRID: 'HYBRID'
};

exports.PricingStrategy = exports.$Enums.PricingStrategy = {
  PENETRATION: 'PENETRATION',
  SKIMMING: 'SKIMMING',
  COMPETITIVE: 'COMPETITIVE',
  VALUE_BASED: 'VALUE_BASED',
  COST_PLUS: 'COST_PLUS',
  DYNAMIC: 'DYNAMIC'
};

exports.GoToMarketStrategy = exports.$Enums.GoToMarketStrategy = {
  DIRECT_SALES: 'DIRECT_SALES',
  INBOUND_MARKETING: 'INBOUND_MARKETING',
  OUTBOUND_MARKETING: 'OUTBOUND_MARKETING',
  PARTNERSHIPS: 'PARTNERSHIPS',
  VIRAL_GROWTH: 'VIRAL_GROWTH',
  COMMUNITY_DRIVEN: 'COMMUNITY_DRIVEN',
  PRODUCT_LED_GROWTH: 'PRODUCT_LED_GROWTH',
  CHANNEL_PARTNERS: 'CHANNEL_PARTNERS'
};

exports.RiskCategory = exports.$Enums.RiskCategory = {
  MARKET: 'MARKET',
  TECHNICAL: 'TECHNICAL',
  FINANCIAL: 'FINANCIAL',
  REGULATORY: 'REGULATORY',
  COMPETITIVE: 'COMPETITIVE',
  OPERATIONAL: 'OPERATIONAL',
  TEAM: 'TEAM',
  TIMING: 'TIMING'
};

exports.Prisma.ModelName = {
  Idea: 'Idea',
  Competitor: 'Competitor',
  CompetitiveMove: 'CompetitiveMove',
  CompetitorSwot: 'CompetitorSwot',
  Project: 'Project',
  Issue: 'Issue',
  IssueDependency: 'IssueDependency',
  IssueLink: 'IssueLink',
  Asset: 'Asset',
  ActivityFeed: 'ActivityFeed',
  PublicRoadmap: 'PublicRoadmap',
  RoadmapItem: 'RoadmapItem',
  RoadmapVote: 'RoadmapVote',
  RoadmapFeedback: 'RoadmapFeedback',
  RoadmapChangelog: 'RoadmapChangelog',
  ChangelogEntry: 'ChangelogEntry',
  FeatureRequest: 'FeatureRequest',
  Waitlist: 'Waitlist',
  WaitlistEntry: 'WaitlistEntry',
  Feature: 'Feature',
  FeatureDependency: 'FeatureDependency',
  FeatureLink: 'FeatureLink',
  Milestone: 'Milestone',
  MilestoneDependency: 'MilestoneDependency',
  Referral: 'Referral',
  AssetView: 'AssetView',
  AssetDownload: 'AssetDownload',
  User: 'User',
  Session: 'Session',
  Account: 'Account',
  Verification: 'Verification',
  Organization: 'Organization',
  Member: 'Member',
  Invitation: 'Invitation',
  Passkey: 'Passkey',
  TwoFactor: 'TwoFactor',
  Subscription: 'Subscription',
  Integration: 'Integration',
  IntegrationUsage: 'IntegrationUsage',
  ApiKey: 'ApiKey',
  ApiCall: 'ApiCall',
  IdeaValidation: 'IdeaValidation',
  ValidationMetrics: 'ValidationMetrics',
  MarketValidation: 'MarketValidation',
  MarketInsight: 'MarketInsight',
  MarketRegionScore: 'MarketRegionScore',
  BusinessValidation: 'BusinessValidation',
  BusinessInsight: 'BusinessInsight',
  RiskAnalysis: 'RiskAnalysis',
  RiskItem: 'RiskItem',
  ProductMarketFitAnalysis: 'ProductMarketFitAnalysis',
  PMFMetric: 'PMFMetric',
  PMFFeedback: 'PMFFeedback',
  MonthlyProjection: 'MonthlyProjection',
  AcquisitionChannel: 'AcquisitionChannel',
  PricingTier: 'PricingTier',
  CompetitorPricing: 'CompetitorPricing',
  CustomerJourneyMapping: 'CustomerJourneyMapping',
  JourneyStage: 'JourneyStage',
  Touchpoint: 'Touchpoint',
  JourneyPainPoint: 'JourneyPainPoint',
  TargetAudienceSegmentation: 'TargetAudienceSegmentation',
  AudienceSegment: 'AudienceSegment',
  MarketTrendAnalysis: 'MarketTrendAnalysis',
  MarketTrend: 'MarketTrend',
  CustomerNeedAnalysis: 'CustomerNeedAnalysis',
  CustomerNeed: 'CustomerNeed',
  PainPoint: 'PainPoint',
  PricingStrategyAnalysis: 'PricingStrategyAnalysis'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
