import React from "react";
import { Clock, User, Plus, GitBranch } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import LoadingSpinner from "@workspace/ui/components/loading-spinner";
import { useSession } from "@/context/session-context";
import { Badge } from "@workspace/ui/components/badge";
import { Separator } from "@workspace/ui/components/separator";
import { useQuery } from "@tanstack/react-query";
import { getFeed } from "@/actions/project/activity-feed";
import { EntityType } from "@workspace/backend/prisma/generated/client/client";

interface ActivityFeedProps {
  entityType: EntityType;
  entityId: string;
  emptyMessage?: string;
  limit?: number;
}

const getActivityIcon = (activity: string) => {
  switch (activity) {
    case "created":
      return <Plus className="h-3 w-3" />;
    case "updated":
      return <div className="h-2 w-2 rounded-full bg-blue-500" />;
    case "phase_changed":
      return <div className="h-2 w-2 rounded-full bg-purple-500" />;
    case "assigned":
      return <User className="h-3 w-3" />;
    case "unassigned":
      return <User className="h-3 w-3 opacity-50" />;
    case "dependency_added":
      return <GitBranch className="h-3 w-3" />;
    case "dependency_removed":
      return <GitBranch className="h-3 w-3 opacity-50" />;
    case "link_added":
      return <div className="h-2 w-2 rounded-full bg-green-500" />;
    case "link_removed":
      return <div className="h-2 w-2 rounded-full bg-red-500" />;
    case "parent_changed":
      return <GitBranch className="h-3 w-3" />;
    default:
      return <div className="h-2 w-2 rounded-full bg-muted-foreground" />;
  }
};

export const ActivityFeed: React.FC<ActivityFeedProps> = ({
  entityType,
  entityId,
  emptyMessage = "No activity yet",
  limit = 20,
}) => {
  const { token } = useSession();

  const { data: activities, isPending } = useQuery({
    queryKey: ["activity-feed", entityType, entityId],
    queryFn: async () => {
      return await getFeed({
        entityType,
        entityId,
      });
    },
  });

  if (isPending) {
    return (
      <div className="flex items-center justify-center py-6">
        <LoadingSpinner />
      </div>
    );
  }

  if (!isPending && activities?.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-muted-foreground">
        <Clock className="h-4 w-4 mb-1.5 opacity-50" />
        <p className="text-sm">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <Separator />
      <div className="flex items-center gap-2">
        <Clock className="h-4 w-4  opacity-50" />
        <p className="text-sm">Recent Activity</p>
      </div>
      <Separator />
      {activities?.map((activity, index) => (
        <div
          key={activity.id}
          className="relative flex items-start gap-2.5 py-2 group hover:bg-muted/20 -mx-2 px-2 transition-colors"
        >
          {/* Timeline line */}
          {index !== activities?.length - 1 && (
            <div className="absolute left-[17.7px] top-[30px] w-px h-full bg-border/60" />
          )}

          {/* Activity icon */}
          <div className="flex-shrink-0 mt-0.5 flex items-center justify-center w-5 h-5 rounded-full bg-background border border-border/60">
            {getActivityIcon(activity.type)}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center justify-between gap-2">
                  <p className="text-sm text-foreground line-clamp-1 whitespace-pre-wrap leading-tight">
                    <span className="font-medium">
                      {activity?.user?.name || "Someone"}
                    </span>
                  </p>
                  <time className="text-xs text-muted-foreground/70 flex-shrink-0 ml-3">
                    {formatDistanceToNow(new Date(activity.createdAt), {
                      addSuffix: true,
                    })}
                  </time>
                </div>
                <p className="text-muted-foreground mt-0.5 text-sm">
                  {activity.title}
                </p>

                {activity.oldValue && activity.newValue && (
                  <div className="mt-2 text-xs">
                    {(activity?.oldValue as string) &&
                      (activity?.newValue as string) && (
                        <div className="flex items-center gap-1 text-muted-foreground">
                          <Badge
                            variant="error"
                            className="capitalize bg-muted/60 rounded text-xs"
                          >
                            {JSON.stringify(
                              activity?.oldValue as string
                            )?.toLocaleLowerCase()}
                          </Badge>
                          <span className="text-muted-foreground/40 mb-1">
                            →
                          </span>
                          <Badge
                            variant="neutral"
                            className="capitalize bg-muted/60 rounded text-xs font-medium"
                          >
                            {activity?.newValue.toLocaleLowerCase()}
                          </Badge>
                        </div>
                      )}
                    {activity?.newValue && !activity?.oldValue && (
                      <span className="capitalize bg-muted/60 rounded text-xs font-medium">
                        {activity?.newValue.toLocaleLowerCase()}
                      </span>
                    )}
                    {activity?.oldValue && !activity?.newValue && (
                      <span className="capitalize bg-muted/60 rounded text-xs line-through text-muted-foreground">
                        {activity?.oldValue.toLocaleLowerCase()}
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ActivityFeed;
