"use server";
import { loops } from "@/lib/loops";

export async function checkLoopsSubscription(email: string) {
  try {
    const res = await loops.findContact({ email });
    if (res[0]?.subscribed) {
      return true;
    } else return false;
  } catch (error) {
    console.error("Error checking Loops subscription:", error);
    return false;
  }
}

export async function subscribeToLoops(email: string) {
  try {
    const response = await fetch(
      "https://app.loops.so/api/v1/contacts/update",
      {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${process.env.LOOPS_API_KEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          subscribed: true,
        }),
      }
    );

    if (!response.ok) {
      throw new Error("Failed to subscribe to Loops");
    }

    return { success: true };
  } catch (error) {
    console.error("Error subscribing to Loops:", error);
    return { success: false, error: "Failed to subscribe" };
  }
}

export async function unsubscribe<PERSON><PERSON><PERSON>oops(email: string) {
  try {
    const response = await fetch(
      "https://app.loops.so/api/v1/contacts/update",
      {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${process.env.LOOPS_API_KEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          subscribed: false,
        }),
      }
    );

    if (!response.ok) {
      throw new Error("Failed to unsubscribe from Loops");
    }

    return { success: true };
  } catch (error) {
    console.error("Error unsubscribing from Loops:", error);
    return { success: false, error: "Failed to unsubscribe" };
  }
}

export async function updateLoopsSubscription(
  email: string,
  subscribed: boolean
) {
  if (subscribed) {
    return await subscribeToLoops(email);
  } else {
    return await unsubscribeFromLoops(email);
  }
}
