{"name": "@workspace/ui", "version": "0.0.1", "type": "module", "private": true, "scripts": {"lint": "biome check .", "format": "biome format . --write", "typecheck": "tsc --noEmit", "test": "vitest run"}, "dependencies": {"@ai-sdk/react": "^2.0.10", "@hello-pangea/dnd": "^18.0.1", "@icons-pack/react-simple-icons": "^13.7.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-toolbar": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-use-controllable-state": "^1.2.2", "ai": "^5.0.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "country-data-list": "^1.5.4", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.12", "harden-react-markdown": "^1.0.4", "input-otp": "^1.4.2", "katex": "^0.16.22", "libphonenumber-js": "^1.12.10", "lucide-react": "^0.528.0", "next-themes": "^0.4.6", "react": "^19.1.1", "react-circle-flags": "^0.0.23", "react-day-picker": "^9.8.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "recharts": "^3.1.2", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwind-variants": "^2.1.0", "tw-animate-css": "^1.3.6", "use-stick-to-bottom": "^1.1.1", "vaul": "^1.1.2", "zod": "^4.0.17"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@turbo/gen": "^2.5.5", "@types/node": "^24.2.1", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@types/react-syntax-highlighter": "^15.5.13", "@workspace/typescript-config": "workspace:*", "tailwindcss": "^4.1.11", "typescript": "^5.9.2"}, "exports": {"./globals.css": "./src/styles/globals.css", "./postcss.config": "./postcss.config.mjs", "./lib/*": "./src/lib/*.ts", "./components/*": "./src/components/*.tsx", "./hooks/*": "./src/hooks/*.ts"}}