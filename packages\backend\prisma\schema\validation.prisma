// Chart-Optimized SaaS Idea Validation Models
// Main validation container for an idea
model IdeaValidation {
    id     String @id @default(uuid())
    ideaId String @unique
    idea   Idea   @relation(fields: [ideaId], references: [id], onDelete: Cascade)

    // Overall validation metrics (chart-friendly)
    overallScore       Float            @default(0) // 0-100
    overallStatus      ValidationStatus @default(PENDING)
    confidenceLevel    Float            @default(0) // 0-100
    validationProgress Float            @default(0) // 0-100 percentage complete

    // Validation timeline
    startedAt     DateTime  @default(now())
    completedAt   DateTime?
    lastUpdatedAt DateTime  @updatedAt

    // Revalidation support fields
    version             Int              @default(1) // Version number for tracking changes
    parentValidationId  String? // Reference to previous validation version
    parentValidation    IdeaValidation?  @relation("ValidationHistory", fields: [parentValidationId], references: [id])
    childValidations    IdeaValidation[] @relation("ValidationHistory")
    isLatest            Boolean          @default(true) // Is this the latest version
    revalidationReason  String? // Why revalidation was triggered
    dataSourcesUpdated  String[] // Which data sources were updated
    lastDataRefresh     DateTime? // When external data was last refreshed
    nextRevalidationDue DateTime? // When next revalidation is recommended

    // Core validation modules
    marketValidation         MarketValidation?
    businessValidation       BusinessValidation?
    riskAnalysis             RiskAnalysis?
    productMarketFitAnalysis ProductMarketFitAnalysis?

    // Consolidated insights (structured for charts/metrics)
    validationMetrics          ValidationMetrics?
    CustomerJourneyMapping     CustomerJourneyMapping?
    TargetAudienceSegmentation TargetAudienceSegmentation?
    MarketTrendAnalysis        MarketTrendAnalysis?
    CustomerNeedAnalysis       CustomerNeedAnalysis?
    PricingStrategyAnalysis    PricingStrategyAnalysis?

    @@map("idea_validation")
}

// Consolidated metrics for better chart visualization
model ValidationMetrics {
    id           String         @id @default(uuid())
    validationId String         @unique
    validation   IdeaValidation @relation(fields: [validationId], references: [id], onDelete: Cascade)

    // Core strength score (0-100) - consolidated from 6 individual scores
    overallStrengthScore Float @default(0)

    // Core risk score (0-100) - consolidated from 6 individual risk scores  
    overallRiskScore Float @default(0)

    // Key performance indicators
    timeToMarket      Int? // Months
    fundingRequired   Float? // USD
    breakEvenMonth    Int? // Months
    customerPayback   Int? // Months
    marketPenetration Float? // Percentage

    // Action priority counts
    immediateActions Int @default(0)
    shortTermActions Int @default(0)
    longTermActions  Int @default(0)

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@map("validation_metrics")
}

// Market validation with chart-optimized structure
model MarketValidation {
    id           String         @id @default(uuid())
    validationId String         @unique
    validation   IdeaValidation @relation(fields: [validationId], references: [id], onDelete: Cascade)

    // Market Size Metrics (chart-friendly numbers)
    totalAddressableMarket       Float? // TAM in millions USD
    serviceableAddressableMarket Float? // SAM in millions USD  
    serviceableObtainableMarket  Float? // SOM in millions USD
    marketGrowthRate             Float? // Annual growth percentage

    // Customer Metrics
    primaryCustomerSegment CustomerSegment
    customerInterviews     Int             @default(0)
    surveyResponses        Int             @default(0)

    // Core validation scores (0-100) - consolidated from multiple individual scores
    overallMarketScore Float @default(0)

    // Geographic distribution
    primaryRegions String[] // Array of region codes
    regionScores   MarketRegionScore[]

    // Validation status
    status ValidationStatus @default(PENDING)

    // Simplified related data
    marketInsights MarketInsight[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@map("market_validation")
}

// Simplified insights table for market data
model MarketInsight {
    id                 String           @id @default(uuid())
    marketValidationId String
    marketValidation   MarketValidation @relation(fields: [marketValidationId], references: [id], onDelete: Cascade)

    category String // "trend", "opportunity", "threat", "pain_point", "success_factor"
    impact   Float  @default(0) // 0-100 impact score
    urgency  Float  @default(0) // 0-100 urgency score

    // Optional details (only when needed for UI)
    label       String?
    description String?

    createdAt DateTime @default(now())

    @@map("market_insights")
}

// Market region scores for geographic visualization
model MarketRegionScore {
    id                 String           @id @default(uuid())
    marketValidationId String
    marketValidation   MarketValidation @relation(fields: [marketValidationId], references: [id], onDelete: Cascade)

    region String // Region code (e.g., "US", "EU", "APAC")
    score  Float  @default(0) // 0-100 market opportunity score for this region

    createdAt DateTime @default(now())

    @@unique([marketValidationId, region])
    @@map("market_region_scores")
}

// Business validation with chart-optimized financial metrics
model BusinessValidation {
    id           String         @id @default(uuid())
    validationId String         @unique
    validation   IdeaValidation @relation(fields: [validationId], references: [id], onDelete: Cascade)

    // Revenue Model (simplified)
    primaryRevenueModel RevenueModel
    pricingStrategy     PricingStrategy
    pricePoint          Float? // Primary price in USD

    // Core Unit Economics (essential metrics only)
    customerAcquisitionCost Float? // CAC in USD
    customerLifetimeValue   Float? // LTV in USD
    monthlyChurnRate        Float? // Monthly churn percentage

    // Financial Projections (time-series data for charts)
    monthlyProjections MonthlyProjection[]

    // Key Financial Metrics
    breakEvenMonth     Int? // Month when revenue >= costs
    initialInvestment  Float? // Required startup capital
    totalFundingNeeded Float? // Total capital required

    // Go-to-Market Metrics
    goToMarketStrategy  GoToMarketStrategy
    acquisitionChannels AcquisitionChannel[]
    salesCycleLength    Int? // Days

    // Core validation scores (0-100) - consolidated from multiple individual scores
    overallBusinessScore Float @default(0)

    // Validation status
    status ValidationStatus @default(PENDING)

    // Simplified related data
    businessInsights BusinessInsight[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@map("business_validation")
}

// Simplified insights table for business data
model BusinessInsight {
    id                   String             @id @default(uuid())
    businessValidationId String
    businessValidation   BusinessValidation @relation(fields: [businessValidationId], references: [id], onDelete: Cascade)

    category String // "risk", "opportunity", "strategy", "metric"
    impact   Float  @default(0) // 0-100 impact score
    urgency  Float  @default(0) // 0-100 urgency score
    cost     Float? // Associated cost in USD (if applicable)

    // Optional details (only when needed for UI)
    label       String?
    description String?

    createdAt DateTime @default(now())

    @@map("business_insights")
}

// Risk analysis model
model RiskAnalysis {
    id           String         @id @default(uuid())
    validationId String         @unique
    validation   IdeaValidation @relation(fields: [validationId], references: [id], onDelete: Cascade)

    overallRiskScore Float            @default(0) // 0-100, calculated from items
    status           ValidationStatus @default(PENDING)
    riskItems        RiskItem[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@map("risk_analysis")
}

// Individual risk item
model RiskItem {
    id             String       @id @default(uuid())
    riskAnalysisId String
    riskAnalysis   RiskAnalysis @relation(fields: [riskAnalysisId], references: [id], onDelete: Cascade)

    category    RiskCategory
    description String
    impact      Int // 1-5 (Low to High)
    probability Int // 1-5 (Low to High)
    mitigation  String // Mitigation plan

    createdAt DateTime @default(now())

    @@map("risk_items")
}

// Product-Market Fit analysis model
model ProductMarketFitAnalysis {
    id           String         @id @default(uuid())
    validationId String         @unique
    validation   IdeaValidation @relation(fields: [validationId], references: [id], onDelete: Cascade)

    pmfScore Float            @default(0) // 0-100
    status   ValidationStatus @default(PENDING)
    metrics  PMFMetric[]
    feedback PMFFeedback[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@map("product_market_fit_analysis")
}

// Metrics for Product-Market Fit
model PMFMetric {
    id                         String                   @id @default(uuid())
    productMarketFitAnalysisId String
    productMarketFitAnalysis   ProductMarketFitAnalysis @relation(fields: [productMarketFitAnalysisId], references: [id], onDelete: Cascade)

    name      String // e.g., "NPS", "Churn Rate", "Activation Rate"
    value     Float
    unit      String // e.g., "%", "score", "users"
    trend     Float? // e.g., monthly change
    benchmark Float? // industry benchmark

    createdAt DateTime @default(now())

    @@unique([productMarketFitAnalysisId, name])
    @@map("pmf_metrics")
}

// Qualitative feedback for Product-Market Fit
model PMFFeedback {
    id                         String                   @id @default(uuid())
    productMarketFitAnalysisId String
    productMarketFitAnalysis   ProductMarketFitAnalysis @relation(fields: [productMarketFitAnalysisId], references: [id], onDelete: Cascade)

    source    String // e.g., "Survey", "User Interview"
    sentiment String // "positive", "negative", "neutral"
    content   String   @db.Text
    tags      String[] // e.g., ["UI", "pricing", "feature_request"]

    createdAt DateTime @default(now())

    @@map("pmf_feedback")
}

// Monthly financial projections for time-series charts
model MonthlyProjection {
    id                   String             @id @default(uuid())
    businessValidationId String
    businessValidation   BusinessValidation @relation(fields: [businessValidationId], references: [id], onDelete: Cascade)

    month   Int // Month number (1-36 for 3-year projection)
    revenue Float @default(0) // Projected revenue for this month
    costs   Float @default(0) // Projected costs for this month
    users   Int   @default(0) // Projected user count for this month

    createdAt DateTime @default(now())

    @@unique([businessValidationId, month])
    @@map("monthly_projections")
}

// Customer acquisition channels with effectiveness scores
model AcquisitionChannel {
    id                   String             @id @default(uuid())
    businessValidationId String
    businessValidation   BusinessValidation @relation(fields: [businessValidationId], references: [id], onDelete: Cascade)

    channel       String // Channel name (e.g., "SEO", "Paid Ads", "Content Marketing")
    effectiveness Float  @default(0) // 0-100 effectiveness score
    cost          Float? // Cost per acquisition for this channel

    createdAt DateTime @default(now())

    @@unique([businessValidationId, channel])
    @@map("acquisition_channels")
}

// =============================================================================
// DEEP RESEARCH FRAMEWORK - 10 Plug & Play Research Modules
// =============================================================================

// Pricing tiers and competitor analysis for pricing strategy
model PricingTier {
    id                        String                  @id @default(uuid())
    pricingStrategyAnalysisId String
    pricingStrategyAnalysis   PricingStrategyAnalysis @relation(fields: [pricingStrategyAnalysisId], references: [id], onDelete: Cascade)

    tierName      String
    tierPrice     Float    @default(0) // Price in USD
    tierFeatures  String[] // Features included
    targetSegment String // Target customer segment

    // Tier performance
    conversionRate   Float @default(0) // 0-100 expected conversion
    popularityScore  Float @default(0) // 0-100 expected popularity
    profitMargin     Float @default(0) // 0-100 profit margin
    competitiveScore Float @default(0) // 0-100 competitive positioning

    createdAt DateTime @default(now())

    @@unique([pricingStrategyAnalysisId, tierName])
    @@map("pricing_tiers")
}

model CompetitorPricing {
    id                        String                  @id @default(uuid())
    pricingStrategyAnalysisId String
    pricingStrategyAnalysis   PricingStrategyAnalysis @relation(fields: [pricingStrategyAnalysisId], references: [id], onDelete: Cascade)

    competitorName String
    pricingModel   String // "subscription", "one-time", "usage-based", "freemium"
    basePrice      Float  @default(0) // Base price in USD
    premiumPrice   Float? // Premium tier price

    // Competitive analysis
    featureComparison Float  @default(0) // 0-100 feature comparison score
    valueComparison   Float  @default(0) // 0-100 value comparison
    marketPosition    String // "premium", "value", "budget"

    // Market response
    marketShare          Float @default(0) // 0-100 estimated market share
    customerSatisfaction Float @default(0) // 0-100 customer satisfaction
    pricingAdvantage     Float @default(0) // -100 to 100 pricing advantage vs us

    createdAt DateTime @default(now())

    @@unique([pricingStrategyAnalysisId, competitorName])
    @@map("competitor_pricing")
}

// 6. Customer Journey Mapping Research
model CustomerJourneyMapping {
    id           String         @id @default(uuid())
    validationId String         @unique
    validation   IdeaValidation @relation(fields: [validationId], references: [id], onDelete: Cascade)

    // Journey overview
    totalJourneyStages Int @default(0)
    averageJourneyTime Int @default(0) // Days

    // Core validation scores (0-100) - consolidated from multiple individual scores
    overallJourneyScore Float @default(0)

    // Journey optimization metrics
    conversionRate       Float @default(0) // Overall journey conversion rate
    dropOffRate          Float @default(0) // Overall drop-off rate
    customerSatisfaction Float @default(0) // 0-100 satisfaction score

    // Related data
    journeyStages     JourneyStage[]
    touchpoints       Touchpoint[]
    journeyPainPoints JourneyPainPoint[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@map("customer_journey_mapping")
}

model JourneyStage {
    id                       String                 @id @default(uuid())
    customerJourneyMappingId String
    customerJourneyMapping   CustomerJourneyMapping @relation(fields: [customerJourneyMappingId], references: [id], onDelete: Cascade)

    stageName       String
    stageOrder      Int // Order in the journey
    averageDuration Int? // Average time spent in this stage (hours)

    // Stage metrics
    conversionRate    Float @default(0) // 0-100 conversion to next stage
    satisfactionScore Float @default(0) // 0-100 satisfaction in this stage
    frictionScore     Float @default(0) // 0-100 friction experienced
    emotionalState    Float @default(0) // -100 to 100 emotional state

    // Stage characteristics
    customerGoals    String[] // What customer wants to achieve
    customerActions  String[] // Actions customer takes
    customerThoughts String[] // Customer thoughts/concerns
    customerEmotions String[] // Customer emotions

    // Business metrics
    dropOffRate    Float @default(0) // 0-100 drop-off rate from this stage
    supportTickets Int   @default(0) // Support tickets generated
    timeToComplete Float @default(0) // Average time to complete stage

    createdAt DateTime @default(now())

    @@unique([customerJourneyMappingId, stageName])
    @@map("journey_stages")
}

model Touchpoint {
    id                       String                 @id @default(uuid())
    customerJourneyMappingId String
    customerJourneyMapping   CustomerJourneyMapping @relation(fields: [customerJourneyMappingId], references: [id], onDelete: Cascade)

    touchpointName String
    touchpointType String // "digital", "physical", "human", "automated"
    channel        String // "website", "email", "phone", "store", etc.
    stageInJourney String // Which journey stage this belongs to

    // Touchpoint performance
    effectivenessScore Float @default(0) // 0-100 effectiveness
    satisfactionScore  Float @default(0) // 0-100 customer satisfaction
    usageFrequency     Float @default(0) // 0-100 how often used
    importanceScore    Float @default(0) // 0-100 importance to journey

    // Optimization metrics
    optimizationPotential Float @default(0) // 0-100 optimization potential
    costEfficiency        Float @default(0) // 0-100 cost efficiency
    automationPotential   Float @default(0) // 0-100 automation potential

    // Context
    customerExpectations String[] // What customers expect
    currentExperience    String? // Current experience description
    improvementAreas     String[] // Areas for improvement

    createdAt DateTime @default(now())

    @@unique([customerJourneyMappingId, touchpointName])
    @@map("touchpoints")
}

model JourneyPainPoint {
    id                       String                 @id @default(uuid())
    customerJourneyMappingId String
    customerJourneyMapping   CustomerJourneyMapping @relation(fields: [customerJourneyMappingId], references: [id], onDelete: Cascade)

    painPointName String
    journeyStage  String // Which stage this pain occurs
    painCategory  String // "process", "information", "emotional", "technical"

    // Pain metrics
    severityScore        Float @default(0) // 0-100 severity
    frequencyScore       Float @default(0) // 0-100 frequency of occurrence
    impactScore          Float @default(0) // 0-100 impact on journey
    resolutionDifficulty Float @default(0) // 0-100 difficulty to resolve

    // Business impact
    dropOffIncrease Float @default(0) // % increase in drop-off due to this pain
    supportCost     Float @default(0) // Cost of support for this pain
    revenueImpact   Float @default(0) // Revenue impact in USD

    // Resolution
    currentMitigation String? // How currently handled
    proposedSolution  String? // Proposed solution
    solutionPriority  Float   @default(0) // 0-100 priority for resolution

    createdAt DateTime @default(now())

    @@unique([customerJourneyMappingId, painPointName])
    @@map("journey_pain_points")
}

// 1. Target Audience Segmentation Research
model TargetAudienceSegmentation {
    id           String         @id @default(uuid())
    validationId String         @unique
    validation   IdeaValidation @relation(fields: [validationId], references: [id], onDelete: Cascade)

    // Segmentation overview
    primarySegment  String
    totalSegments   Int    @default(0)
    totalMarketSize Int    @default(0) // Total addressable market size

    // Core validation scores (0-100) - consolidated from multiple individual scores
    overallSegmentationScore Float @default(0)

    // Key metrics
    averageSegmentSize   Int   @default(0) // Average segment size
    segmentAccessibility Float @default(0) // 0-100 ease of reaching segments
    marketPenetration    Float @default(0) // Expected market penetration %

    // Related data
    audienceSegments AudienceSegment[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@map("target_audience_segmentation")
}

model AudienceSegment {
    id                           String                     @id @default(uuid())
    targetAudienceSegmentationId String
    targetAudienceSegmentation   TargetAudienceSegmentation @relation(fields: [targetAudienceSegmentationId], references: [id], onDelete: Cascade)

    segmentName         String
    segmentSize         Float  @default(0) // Percentage of total market
    attractivenessScore Float  @default(0) // 0-100 score
    accessibilityScore  Float  @default(0) // 0-100 ease of reach
    profitabilityScore  Float  @default(0) // 0-100 profit potential

    // Key characteristics
    primaryNeed       String?
    secondaryNeeds    String[]
    preferredSolution String?
    budgetRange       String?

    createdAt DateTime @default(now())

    @@unique([targetAudienceSegmentationId, segmentName])
    @@map("audience_segments")
}

// 2. Market Trend Analysis Research
model MarketTrendAnalysis {
    id           String         @id @default(uuid())
    validationId String         @unique
    validation   IdeaValidation @relation(fields: [validationId], references: [id], onDelete: Cascade)

    // Trend analysis overview
    primaryTrend       String
    totalTrendsTracked Int    @default(0)
    analysisTimeframe  Int    @default(12) // Months of trend analysis

    // Core validation scores (0-100) - consolidated from multiple individual scores
    overallTrendScore Float @default(0)

    // Key trend metrics
    trendStrength    Float @default(0) // 0-100 strength of primary trend
    marketGrowthRate Float @default(0) // Annual market growth percentage
    adoptionRate     Float @default(0) // Technology/trend adoption rate

    // Related data
    marketTrends MarketTrend[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@map("market_trend_analysis")
}

model MarketTrend {
    id                    String              @id @default(uuid())
    marketTrendAnalysisId String
    marketTrendAnalysis   MarketTrendAnalysis @relation(fields: [marketTrendAnalysisId], references: [id], onDelete: Cascade)

    trendName      String
    trendCategory  String // "technology", "social", "economic", "regulatory"
    impactScore    Float  @default(0) // 0-100 impact on business
    timelineMonths Int? // Expected timeline
    certaintyLevel Float  @default(0) // 0-100 certainty of trend

    // Opportunity/threat assessment
    opportunityScore Float @default(0) // 0-100 opportunity potential
    threatScore      Float @default(0) // 0-100 threat level

    description String?

    createdAt DateTime @default(now())

    @@unique([marketTrendAnalysisId, trendName])
    @@map("market_trends")
}

// 3. Customer Needs & Pain Points Research
model CustomerNeedAnalysis {
    id           String         @id @default(uuid())
    validationId String         @unique
    validation   IdeaValidation @relation(fields: [validationId], references: [id], onDelete: Cascade)

    // Analysis overview
    primaryNeed          String
    totalNeedsIdentified Int    @default(0)
    totalPainPoints      Int    @default(0)

    // Core validation scores (0-100) - consolidated from multiple individual scores
    overallNeedScore Float @default(0)

    // Key metrics
    needUrgency         Float @default(0) // 0-100 urgency of primary need
    solutionGap         Float @default(0) // 0-100 gap in current solutions
    customerWillingness Float @default(0) // 0-100 willingness to pay for solution

    // Related data
    customerNeeds CustomerNeed[]
    painPoints    PainPoint[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@map("customer_need_analysis")
}

model CustomerNeed {
    id                     String               @id @default(uuid())
    customerNeedAnalysisId String
    customerNeedAnalysis   CustomerNeedAnalysis @relation(fields: [customerNeedAnalysisId], references: [id], onDelete: Cascade)

    needName        String
    needCategory    String // "functional", "emotional", "social"
    intensityScore  Float  @default(0) // 0-100 intensity
    frequencyScore  Float  @default(0) // 0-100 frequency
    urgencyScore    Float  @default(0) // 0-100 urgency
    satisfactionGap Float  @default(0) // 0-100 gap in current satisfaction

    // Context
    triggerEvents   String[] // What triggers this need
    desiredOutcome  String? // What success looks like
    currentSolution String? // How they solve it now

    createdAt DateTime @default(now())

    @@unique([customerNeedAnalysisId, needName])
    @@map("customer_needs")
}

model PainPoint {
    id                     String               @id @default(uuid())
    customerNeedAnalysisId String
    customerNeedAnalysis   CustomerNeedAnalysis @relation(fields: [customerNeedAnalysisId], references: [id], onDelete: Cascade)

    painName       String
    painCategory   String // "process", "cost", "time", "quality", "experience"
    severityScore  Float  @default(0) // 0-100 severity
    frequencyScore Float  @default(0) // 0-100 frequency
    impactScore    Float  @default(0) // 0-100 business impact
    emotionalToll  Float  @default(0) // 0-100 emotional impact

    // Cost analysis
    timeCostHours   Float? // Hours lost per occurrence
    financialCost   Float? // Dollar cost per occurrence
    opportunityCost Float? // Missed opportunities cost

    // Context
    painTriggers      String[] // What causes this pain
    currentMitigation String? // How they currently handle it

    createdAt DateTime @default(now())

    @@unique([customerNeedAnalysisId, painName])
    @@map("pain_points")
}

// 4. Pricing Strategy Analysis Research
model PricingStrategyAnalysis {
    id           String         @id @default(uuid())
    validationId String         @unique
    validation   IdeaValidation @relation(fields: [validationId], references: [id], onDelete: Cascade)

    // Pricing strategy overview
    primaryStrategy    PricingStrategy
    recommendedPrice   Float? // Primary recommended price point
    totalTiersAnalyzed Int             @default(0)

    // Core validation scores (0-100) - consolidated from multiple individual scores
    overallPricingScore Float @default(0)

    // Key pricing metrics
    priceAcceptance      Float @default(0) // 0-100 customer price acceptance
    competitivenessScore Float @default(0) // 0-100 competitive positioning
    profitabilityScore   Float @default(0) // 0-100 profitability potential

    // Related data
    pricingTiers      PricingTier[]
    competitorPricing CompetitorPricing[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@map("pricing_strategy_analysis")
}
