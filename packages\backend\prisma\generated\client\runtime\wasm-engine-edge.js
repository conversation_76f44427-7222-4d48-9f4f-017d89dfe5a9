
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */
"use strict";var Xo=Object.create;var kt=Object.defineProperty;var Zo=Object.getOwnPropertyDescriptor;var es=Object.getOwnPropertyNames;var ts=Object.getPrototypeOf,rs=Object.prototype.hasOwnProperty;var ie=(t,e)=>()=>(t&&(e=t(t=0)),e);var Fe=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),nt=(t,e)=>{for(var r in e)kt(t,r,{get:e[r],enumerable:!0})},mn=(t,e,r,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of es(e))!rs.call(t,i)&&i!==r&&kt(t,i,{get:()=>e[i],enumerable:!(n=Zo(e,i))||n.enumerable});return t};var it=(t,e,r)=>(r=t!=null?Xo(ts(t)):{},mn(e||!t||!t.__esModule?kt(r,"default",{value:t,enumerable:!0}):r,t)),ns=t=>mn(kt({},"__esModule",{value:!0}),t);function Er(t,e){if(e=e.toLowerCase(),e==="utf8"||e==="utf-8")return new y(as.encode(t));if(e==="base64"||e==="base64url")return t=t.replace(/-/g,"+").replace(/_/g,"/"),t=t.replace(/[^A-Za-z0-9+/]/g,""),new y([...atob(t)].map(r=>r.charCodeAt(0)));if(e==="binary"||e==="ascii"||e==="latin1"||e==="latin-1")return new y([...t].map(r=>r.charCodeAt(0)));if(e==="ucs2"||e==="ucs-2"||e==="utf16le"||e==="utf-16le"){let r=new y(t.length*2),n=new DataView(r.buffer);for(let i=0;i<t.length;i++)n.setUint16(i*2,t.charCodeAt(i),!0);return r}if(e==="hex"){let r=new y(t.length/2);for(let n=0,i=0;i<t.length;i+=2,n++)r[n]=parseInt(t.slice(i,i+2),16);return r}dn(`encoding "${e}"`)}function is(t){let r=Object.getOwnPropertyNames(DataView.prototype).filter(a=>a.startsWith("get")||a.startsWith("set")),n=r.map(a=>a.replace("get","read").replace("set","write")),i=(a,f)=>function(h=0){return V(h,"offset"),X(h,"offset"),$(h,"offset",this.length-1),new DataView(this.buffer)[r[a]](h,f)},o=(a,f)=>function(h,C=0){let R=r[a].match(/set(\w+\d+)/)[1].toLowerCase(),k=ss[R];return V(C,"offset"),X(C,"offset"),$(C,"offset",this.length-1),os(h,"value",k[0],k[1]),new DataView(this.buffer)[r[a]](C,h,f),C+parseInt(r[a].match(/\d+/)[0])/8},s=a=>{a.forEach(f=>{f.includes("Uint")&&(t[f.replace("Uint","UInt")]=t[f]),f.includes("Float64")&&(t[f.replace("Float64","Double")]=t[f]),f.includes("Float32")&&(t[f.replace("Float32","Float")]=t[f])})};n.forEach((a,f)=>{a.startsWith("read")&&(t[a]=i(f,!1),t[a+"LE"]=i(f,!0),t[a+"BE"]=i(f,!1)),a.startsWith("write")&&(t[a]=o(f,!1),t[a+"LE"]=o(f,!0),t[a+"BE"]=o(f,!1)),s([a,a+"LE",a+"BE"])})}function dn(t){throw new Error(`Buffer polyfill does not implement "${t}"`)}function Dt(t,e){if(!(t instanceof Uint8Array))throw new TypeError(`The "${e}" argument must be an instance of Buffer or Uint8Array`)}function $(t,e,r=cs+1){if(t<0||t>r){let n=new RangeError(`The value of "${e}" is out of range. It must be >= 0 && <= ${r}. Received ${t}`);throw n.code="ERR_OUT_OF_RANGE",n}}function V(t,e){if(typeof t!="number"){let r=new TypeError(`The "${e}" argument must be of type number. Received type ${typeof t}.`);throw r.code="ERR_INVALID_ARG_TYPE",r}}function X(t,e){if(!Number.isInteger(t)||Number.isNaN(t)){let r=new RangeError(`The value of "${e}" is out of range. It must be an integer. Received ${t}`);throw r.code="ERR_OUT_OF_RANGE",r}}function os(t,e,r,n){if(t<r||t>n){let i=new RangeError(`The value of "${e}" is out of range. It must be >= ${r} and <= ${n}. Received ${t}`);throw i.code="ERR_OUT_OF_RANGE",i}}function pn(t,e){if(typeof t!="string"){let r=new TypeError(`The "${e}" argument must be of type string. Received type ${typeof t}`);throw r.code="ERR_INVALID_ARG_TYPE",r}}function ms(t,e="utf8"){return y.from(t,e)}var y,ss,as,ls,us,cs,b,Pr,u=ie(()=>{"use strict";y=class t extends Uint8Array{_isBuffer=!0;get offset(){return this.byteOffset}static alloc(e,r=0,n="utf8"){return pn(n,"encoding"),t.allocUnsafe(e).fill(r,n)}static allocUnsafe(e){return t.from(e)}static allocUnsafeSlow(e){return t.from(e)}static isBuffer(e){return e&&!!e._isBuffer}static byteLength(e,r="utf8"){if(typeof e=="string")return Er(e,r).byteLength;if(e&&e.byteLength)return e.byteLength;let n=new TypeError('The "string" argument must be of type string or an instance of Buffer or ArrayBuffer.');throw n.code="ERR_INVALID_ARG_TYPE",n}static isEncoding(e){return us.includes(e)}static compare(e,r){Dt(e,"buff1"),Dt(r,"buff2");for(let n=0;n<e.length;n++){if(e[n]<r[n])return-1;if(e[n]>r[n])return 1}return e.length===r.length?0:e.length>r.length?1:-1}static from(e,r="utf8"){if(e&&typeof e=="object"&&e.type==="Buffer")return new t(e.data);if(typeof e=="number")return new t(new Uint8Array(e));if(typeof e=="string")return Er(e,r);if(ArrayBuffer.isView(e)){let{byteOffset:n,byteLength:i,buffer:o}=e;return"map"in e&&typeof e.map=="function"?new t(e.map(s=>s%256),n,i):new t(o,n,i)}if(e&&typeof e=="object"&&("length"in e||"byteLength"in e||"buffer"in e))return new t(e);throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}static concat(e,r){if(e.length===0)return t.alloc(0);let n=[].concat(...e.map(o=>[...o])),i=t.alloc(r!==void 0?r:n.length);return i.set(r!==void 0?n.slice(0,r):n),i}slice(e=0,r=this.length){return this.subarray(e,r)}subarray(e=0,r=this.length){return Object.setPrototypeOf(super.subarray(e,r),t.prototype)}reverse(){return super.reverse(),this}readIntBE(e,r){V(e,"offset"),X(e,"offset"),$(e,"offset",this.length-1),V(r,"byteLength"),X(r,"byteLength");let n=new DataView(this.buffer,e,r),i=0;for(let o=0;o<r;o++)i=i*256+n.getUint8(o);return n.getUint8(0)&128&&(i-=Math.pow(256,r)),i}readIntLE(e,r){V(e,"offset"),X(e,"offset"),$(e,"offset",this.length-1),V(r,"byteLength"),X(r,"byteLength");let n=new DataView(this.buffer,e,r),i=0;for(let o=0;o<r;o++)i+=n.getUint8(o)*Math.pow(256,o);return n.getUint8(r-1)&128&&(i-=Math.pow(256,r)),i}readUIntBE(e,r){V(e,"offset"),X(e,"offset"),$(e,"offset",this.length-1),V(r,"byteLength"),X(r,"byteLength");let n=new DataView(this.buffer,e,r),i=0;for(let o=0;o<r;o++)i=i*256+n.getUint8(o);return i}readUintBE(e,r){return this.readUIntBE(e,r)}readUIntLE(e,r){V(e,"offset"),X(e,"offset"),$(e,"offset",this.length-1),V(r,"byteLength"),X(r,"byteLength");let n=new DataView(this.buffer,e,r),i=0;for(let o=0;o<r;o++)i+=n.getUint8(o)*Math.pow(256,o);return i}readUintLE(e,r){return this.readUIntLE(e,r)}writeIntBE(e,r,n){return e=e<0?e+Math.pow(256,n):e,this.writeUIntBE(e,r,n)}writeIntLE(e,r,n){return e=e<0?e+Math.pow(256,n):e,this.writeUIntLE(e,r,n)}writeUIntBE(e,r,n){V(r,"offset"),X(r,"offset"),$(r,"offset",this.length-1),V(n,"byteLength"),X(n,"byteLength");let i=new DataView(this.buffer,r,n);for(let o=n-1;o>=0;o--)i.setUint8(o,e&255),e=e/256;return r+n}writeUintBE(e,r,n){return this.writeUIntBE(e,r,n)}writeUIntLE(e,r,n){V(r,"offset"),X(r,"offset"),$(r,"offset",this.length-1),V(n,"byteLength"),X(n,"byteLength");let i=new DataView(this.buffer,r,n);for(let o=0;o<n;o++)i.setUint8(o,e&255),e=e/256;return r+n}writeUintLE(e,r,n){return this.writeUIntLE(e,r,n)}toJSON(){return{type:"Buffer",data:Array.from(this)}}swap16(){let e=new DataView(this.buffer,this.byteOffset,this.byteLength);for(let r=0;r<this.length;r+=2)e.setUint16(r,e.getUint16(r,!0),!1);return this}swap32(){let e=new DataView(this.buffer,this.byteOffset,this.byteLength);for(let r=0;r<this.length;r+=4)e.setUint32(r,e.getUint32(r,!0),!1);return this}swap64(){let e=new DataView(this.buffer,this.byteOffset,this.byteLength);for(let r=0;r<this.length;r+=8)e.setBigUint64(r,e.getBigUint64(r,!0),!1);return this}compare(e,r=0,n=e.length,i=0,o=this.length){return Dt(e,"target"),V(r,"targetStart"),V(n,"targetEnd"),V(i,"sourceStart"),V(o,"sourceEnd"),$(r,"targetStart"),$(n,"targetEnd",e.length),$(i,"sourceStart"),$(o,"sourceEnd",this.length),t.compare(this.slice(i,o),e.slice(r,n))}equals(e){return Dt(e,"otherBuffer"),this.length===e.length&&this.every((r,n)=>r===e[n])}copy(e,r=0,n=0,i=this.length){$(r,"targetStart"),$(n,"sourceStart",this.length),$(i,"sourceEnd"),r>>>=0,n>>>=0,i>>>=0;let o=0;for(;n<i&&!(this[n]===void 0||e[r]===void 0);)e[r]=this[n],o++,n++,r++;return o}write(e,r,n,i="utf8"){let o=typeof r=="string"?0:r??0,s=typeof n=="string"?this.length-o:n??this.length-o;return i=typeof r=="string"?r:typeof n=="string"?n:i,V(o,"offset"),V(s,"length"),$(o,"offset",this.length),$(s,"length",this.length),(i==="ucs2"||i==="ucs-2"||i==="utf16le"||i==="utf-16le")&&(s=s-s%2),Er(e,i).copy(this,o,0,s)}fill(e=0,r=0,n=this.length,i="utf-8"){let o=typeof r=="string"?0:r,s=typeof n=="string"?this.length:n;if(i=typeof r=="string"?r:typeof n=="string"?n:i,e=t.from(typeof e=="number"?[e]:e??[],i),pn(i,"encoding"),$(o,"offset",this.length),$(s,"end",this.length),e.length!==0)for(let a=o;a<s;a+=e.length)super.set(e.slice(0,e.length+a>=this.length?this.length-a:e.length),a);return this}includes(e,r=null,n="utf-8"){return this.indexOf(e,r,n)!==-1}lastIndexOf(e,r=null,n="utf-8"){return this.indexOf(e,r,n,!0)}indexOf(e,r=null,n="utf-8",i=!1){let o=i?this.findLastIndex.bind(this):this.findIndex.bind(this);n=typeof r=="string"?r:n;let s=t.from(typeof e=="number"?[e]:e,n),a=typeof r=="string"?0:r;return a=typeof r=="number"?a:null,a=Number.isNaN(a)?null:a,a??=i?this.length:0,a=a<0?this.length+a:a,s.length===0&&i===!1?a>=this.length?this.length:a:s.length===0&&i===!0?(a>=this.length?this.length:a)||this.length:o((f,h)=>(i?h<=a:h>=a)&&this[h]===s[0]&&s.every((R,k)=>this[h+k]===R))}toString(e="utf8",r=0,n=this.length){if(r=r<0?0:r,e=e.toString().toLowerCase(),n<=0)return"";if(e==="utf8"||e==="utf-8")return ls.decode(this.slice(r,n));if(e==="base64"||e==="base64url"){let i=btoa(this.reduce((o,s)=>o+Pr(s),""));return e==="base64url"?i.replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""):i}if(e==="binary"||e==="ascii"||e==="latin1"||e==="latin-1")return this.slice(r,n).reduce((i,o)=>i+Pr(o&(e==="ascii"?127:255)),"");if(e==="ucs2"||e==="ucs-2"||e==="utf16le"||e==="utf-16le"){let i=new DataView(this.buffer.slice(r,n));return Array.from({length:i.byteLength/2},(o,s)=>s*2+1<i.byteLength?Pr(i.getUint16(s*2,!0)):"").join("")}if(e==="hex")return this.slice(r,n).reduce((i,o)=>i+o.toString(16).padStart(2,"0"),"");dn(`encoding "${e}"`)}toLocaleString(){return this.toString()}inspect(){return`<Buffer ${this.toString("hex").match(/.{1,2}/g).join(" ")}>`}};ss={int8:[-128,127],int16:[-32768,32767],int32:[-2147483648,2147483647],uint8:[0,255],uint16:[0,65535],uint32:[0,4294967295],float32:[-1/0,1/0],float64:[-1/0,1/0],bigint64:[-0x8000000000000000n,0x7fffffffffffffffn],biguint64:[0n,0xffffffffffffffffn]},as=new TextEncoder,ls=new TextDecoder,us=["utf8","utf-8","hex","base64","ascii","binary","base64url","ucs2","ucs-2","utf16le","utf-16le","latin1","latin-1"],cs=4294967295;is(y.prototype);b=new Proxy(ms,{construct(t,[e,r]){return y.from(e,r)},get(t,e){return y[e]}}),Pr=String.fromCodePoint});var g,E,c=ie(()=>{"use strict";g={nextTick:(t,...e)=>{setTimeout(()=>{t(...e)},0)},env:{},version:"",cwd:()=>"/",stderr:{},argv:["/bin/node"],pid:1e4},{cwd:E}=g});var P,m=ie(()=>{"use strict";P=globalThis.performance??(()=>{let t=Date.now();return{now:()=>Date.now()-t}})()});var x,p=ie(()=>{"use strict";x=()=>{};x.prototype=x});var w,d=ie(()=>{"use strict";w=class{value;constructor(e){this.value=e}deref(){return this.value}}});function hn(t,e){var r,n,i,o,s,a,f,h,C=t.constructor,R=C.precision;if(!t.s||!e.s)return e.s||(e=new C(t)),q?L(e,R):e;if(f=t.d,h=e.d,s=t.e,i=e.e,f=f.slice(),o=s-i,o){for(o<0?(n=f,o=-o,a=h.length):(n=h,i=s,a=f.length),s=Math.ceil(R/N),a=s>a?s+1:a+1,o>a&&(o=a,n.length=1),n.reverse();o--;)n.push(0);n.reverse()}for(a=f.length,o=h.length,a-o<0&&(o=a,n=h,h=f,f=n),r=0;o;)r=(f[--o]=f[o]+h[o]+r)/J|0,f[o]%=J;for(r&&(f.unshift(r),++i),a=f.length;f[--a]==0;)f.pop();return e.d=f,e.e=i,q?L(e,R):e}function ce(t,e,r){if(t!==~~t||t<e||t>r)throw Error(ke+t)}function ue(t){var e,r,n,i=t.length-1,o="",s=t[0];if(i>0){for(o+=s,e=1;e<i;e++)n=t[e]+"",r=N-n.length,r&&(o+=Pe(r)),o+=n;s=t[e],n=s+"",r=N-n.length,r&&(o+=Pe(r))}else if(s===0)return"0";for(;s%10===0;)s/=10;return o+s}function bn(t,e){var r,n,i,o,s,a,f=0,h=0,C=t.constructor,R=C.precision;if(j(t)>16)throw Error(Tr+j(t));if(!t.s)return new C(te);for(e==null?(q=!1,a=R):a=e,s=new C(.03125);t.abs().gte(.1);)t=t.times(s),h+=5;for(n=Math.log(Oe(2,h))/Math.LN10*2+5|0,a+=n,r=i=o=new C(te),C.precision=a;;){if(i=L(i.times(t),a),r=r.times(++f),s=o.plus(he(i,r,a)),ue(s.d).slice(0,a)===ue(o.d).slice(0,a)){for(;h--;)o=L(o.times(o),a);return C.precision=R,e==null?(q=!0,L(o,R)):o}o=s}}function j(t){for(var e=t.e*N,r=t.d[0];r>=10;r/=10)e++;return e}function vr(t,e,r){if(e>t.LN10.sd())throw q=!0,r&&(t.precision=r),Error(oe+"LN10 precision limit exceeded");return L(new t(t.LN10),e)}function Pe(t){for(var e="";t--;)e+="0";return e}function ot(t,e){var r,n,i,o,s,a,f,h,C,R=1,k=10,A=t,_=A.d,O=A.constructor,D=O.precision;if(A.s<1)throw Error(oe+(A.s?"NaN":"-Infinity"));if(A.eq(te))return new O(0);if(e==null?(q=!1,h=D):h=e,A.eq(10))return e==null&&(q=!0),vr(O,h);if(h+=k,O.precision=h,r=ue(_),n=r.charAt(0),o=j(A),Math.abs(o)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)A=A.times(t),r=ue(A.d),n=r.charAt(0),R++;o=j(A),n>1?(A=new O("0."+r),o++):A=new O(n+"."+r.slice(1))}else return f=vr(O,h+2,D).times(o+""),A=ot(new O(n+"."+r.slice(1)),h-k).plus(f),O.precision=D,e==null?(q=!0,L(A,D)):A;for(a=s=A=he(A.minus(te),A.plus(te),h),C=L(A.times(A),h),i=3;;){if(s=L(s.times(C),h),f=a.plus(he(s,new O(i),h)),ue(f.d).slice(0,h)===ue(a.d).slice(0,h))return a=a.times(2),o!==0&&(a=a.plus(vr(O,h+2,D).times(o+""))),a=he(a,new O(R),h),O.precision=D,e==null?(q=!0,L(a,D)):a;a=f,i+=2}}function fn(t,e){var r,n,i;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;e.charCodeAt(n)===48;)++n;for(i=e.length;e.charCodeAt(i-1)===48;)--i;if(e=e.slice(n,i),e){if(i-=n,r=r-n-1,t.e=Ne(r/N),t.d=[],n=(r+1)%N,r<0&&(n+=N),n<i){for(n&&t.d.push(+e.slice(0,n)),i-=N;n<i;)t.d.push(+e.slice(n,n+=N));e=e.slice(n),n=N-e.length}else n-=i;for(;n--;)e+="0";if(t.d.push(+e),q&&(t.e>It||t.e<-It))throw Error(Tr+r)}else t.s=0,t.e=0,t.d=[0];return t}function L(t,e,r){var n,i,o,s,a,f,h,C,R=t.d;for(s=1,o=R[0];o>=10;o/=10)s++;if(n=e-s,n<0)n+=N,i=e,h=R[C=0];else{if(C=Math.ceil((n+1)/N),o=R.length,C>=o)return t;for(h=o=R[C],s=1;o>=10;o/=10)s++;n%=N,i=n-N+s}if(r!==void 0&&(o=Oe(10,s-i-1),a=h/o%10|0,f=e<0||R[C+1]!==void 0||h%o,f=r<4?(a||f)&&(r==0||r==(t.s<0?3:2)):a>5||a==5&&(r==4||f||r==6&&(n>0?i>0?h/Oe(10,s-i):0:R[C-1])%10&1||r==(t.s<0?8:7))),e<1||!R[0])return f?(o=j(t),R.length=1,e=e-o-1,R[0]=Oe(10,(N-e%N)%N),t.e=Ne(-e/N)||0):(R.length=1,R[0]=t.e=t.s=0),t;if(n==0?(R.length=C,o=1,C--):(R.length=C+1,o=Oe(10,N-n),R[C]=i>0?(h/Oe(10,s-i)%Oe(10,i)|0)*o:0),f)for(;;)if(C==0){(R[0]+=o)==J&&(R[0]=1,++t.e);break}else{if(R[C]+=o,R[C]!=J)break;R[C--]=0,o=1}for(n=R.length;R[--n]===0;)R.pop();if(q&&(t.e>It||t.e<-It))throw Error(Tr+j(t));return t}function wn(t,e){var r,n,i,o,s,a,f,h,C,R,k=t.constructor,A=k.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new k(t),q?L(e,A):e;if(f=t.d,R=e.d,n=e.e,h=t.e,f=f.slice(),s=h-n,s){for(C=s<0,C?(r=f,s=-s,a=R.length):(r=R,n=h,a=f.length),i=Math.max(Math.ceil(A/N),a)+2,s>i&&(s=i,r.length=1),r.reverse(),i=s;i--;)r.push(0);r.reverse()}else{for(i=f.length,a=R.length,C=i<a,C&&(a=i),i=0;i<a;i++)if(f[i]!=R[i]){C=f[i]<R[i];break}s=0}for(C&&(r=f,f=R,R=r,e.s=-e.s),a=f.length,i=R.length-a;i>0;--i)f[a++]=0;for(i=R.length;i>s;){if(f[--i]<R[i]){for(o=i;o&&f[--o]===0;)f[o]=J-1;--f[o],f[i]+=J}f[i]-=R[i]}for(;f[--a]===0;)f.pop();for(;f[0]===0;f.shift())--n;return f[0]?(e.d=f,e.e=n,q?L(e,A):e):new k(0)}function De(t,e,r){var n,i=j(t),o=ue(t.d),s=o.length;return e?(r&&(n=r-s)>0?o=o.charAt(0)+"."+o.slice(1)+Pe(n):s>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(i<0?"e":"e+")+i):i<0?(o="0."+Pe(-i-1)+o,r&&(n=r-s)>0&&(o+=Pe(n))):i>=s?(o+=Pe(i+1-s),r&&(n=r-i-1)>0&&(o=o+"."+Pe(n))):((n=i+1)<s&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-s)>0&&(i+1===s&&(o+="."),o+=Pe(n))),t.s<0?"-"+o:o}function gn(t,e){if(t.length>e)return t.length=e,!0}function xn(t){var e,r,n;function i(o){var s=this;if(!(s instanceof i))return new i(o);if(s.constructor=i,o instanceof i){s.s=o.s,s.e=o.e,s.d=(o=o.d)?o.slice():o;return}if(typeof o=="number"){if(o*0!==0)throw Error(ke+o);if(o>0)s.s=1;else if(o<0)o=-o,s.s=-1;else{s.s=0,s.e=0,s.d=[0];return}if(o===~~o&&o<1e7){s.e=0,s.d=[o];return}return fn(s,o.toString())}else if(typeof o!="string")throw Error(ke+o);if(o.charCodeAt(0)===45?(o=o.slice(1),s.s=-1):s.s=1,ds.test(o))fn(s,o);else throw Error(ke+o)}if(i.prototype=S,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=xn,i.config=i.set=fs,t===void 0&&(t={}),t)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],e=0;e<n.length;)t.hasOwnProperty(r=n[e++])||(t[r]=this[r]);return i.config(t),i}function fs(t){if(!t||typeof t!="object")throw Error(oe+"Object expected");var e,r,n,i=["precision",1,Ue,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<i.length;e+=3)if((n=t[r=i[e]])!==void 0)if(Ne(n)===n&&n>=i[e+1]&&n<=i[e+2])this[r]=n;else throw Error(ke+r+": "+n);if((n=t[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(ke+r+": "+n);return this}var Ue,ps,Cr,q,oe,ke,Tr,Ne,Oe,ds,te,J,N,yn,It,S,he,Cr,Mt,En=ie(()=>{"use strict";u();c();m();p();d();l();Ue=1e9,ps={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},q=!0,oe="[DecimalError] ",ke=oe+"Invalid argument: ",Tr=oe+"Exponent out of range: ",Ne=Math.floor,Oe=Math.pow,ds=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,J=1e7,N=7,yn=9007199254740991,It=Ne(yn/N),S={};S.absoluteValue=S.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t};S.comparedTo=S.cmp=function(t){var e,r,n,i,o=this;if(t=new o.constructor(t),o.s!==t.s)return o.s||-t.s;if(o.e!==t.e)return o.e>t.e^o.s<0?1:-1;for(n=o.d.length,i=t.d.length,e=0,r=n<i?n:i;e<r;++e)if(o.d[e]!==t.d[e])return o.d[e]>t.d[e]^o.s<0?1:-1;return n===i?0:n>i^o.s<0?1:-1};S.decimalPlaces=S.dp=function(){var t=this,e=t.d.length-1,r=(e-t.e)*N;if(e=t.d[e],e)for(;e%10==0;e/=10)r--;return r<0?0:r};S.dividedBy=S.div=function(t){return he(this,new this.constructor(t))};S.dividedToIntegerBy=S.idiv=function(t){var e=this,r=e.constructor;return L(he(e,new r(t),0,1),r.precision)};S.equals=S.eq=function(t){return!this.cmp(t)};S.exponent=function(){return j(this)};S.greaterThan=S.gt=function(t){return this.cmp(t)>0};S.greaterThanOrEqualTo=S.gte=function(t){return this.cmp(t)>=0};S.isInteger=S.isint=function(){return this.e>this.d.length-2};S.isNegative=S.isneg=function(){return this.s<0};S.isPositive=S.ispos=function(){return this.s>0};S.isZero=function(){return this.s===0};S.lessThan=S.lt=function(t){return this.cmp(t)<0};S.lessThanOrEqualTo=S.lte=function(t){return this.cmp(t)<1};S.logarithm=S.log=function(t){var e,r=this,n=r.constructor,i=n.precision,o=i+5;if(t===void 0)t=new n(10);else if(t=new n(t),t.s<1||t.eq(te))throw Error(oe+"NaN");if(r.s<1)throw Error(oe+(r.s?"NaN":"-Infinity"));return r.eq(te)?new n(0):(q=!1,e=he(ot(r,o),ot(t,o),o),q=!0,L(e,i))};S.minus=S.sub=function(t){var e=this;return t=new e.constructor(t),e.s==t.s?wn(e,t):hn(e,(t.s=-t.s,t))};S.modulo=S.mod=function(t){var e,r=this,n=r.constructor,i=n.precision;if(t=new n(t),!t.s)throw Error(oe+"NaN");return r.s?(q=!1,e=he(r,t,0,1).times(t),q=!0,r.minus(e)):L(new n(r),i)};S.naturalExponential=S.exp=function(){return bn(this)};S.naturalLogarithm=S.ln=function(){return ot(this)};S.negated=S.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t};S.plus=S.add=function(t){var e=this;return t=new e.constructor(t),e.s==t.s?hn(e,t):wn(e,(t.s=-t.s,t))};S.precision=S.sd=function(t){var e,r,n,i=this;if(t!==void 0&&t!==!!t&&t!==1&&t!==0)throw Error(ke+t);if(e=j(i)+1,n=i.d.length-1,r=n*N+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return t&&e>r?e:r};S.squareRoot=S.sqrt=function(){var t,e,r,n,i,o,s,a=this,f=a.constructor;if(a.s<1){if(!a.s)return new f(0);throw Error(oe+"NaN")}for(t=j(a),q=!1,i=Math.sqrt(+a),i==0||i==1/0?(e=ue(a.d),(e.length+t)%2==0&&(e+="0"),i=Math.sqrt(e),t=Ne((t+1)/2)-(t<0||t%2),i==1/0?e="5e"+t:(e=i.toExponential(),e=e.slice(0,e.indexOf("e")+1)+t),n=new f(e)):n=new f(i.toString()),r=f.precision,i=s=r+3;;)if(o=n,n=o.plus(he(a,o,s+2)).times(.5),ue(o.d).slice(0,s)===(e=ue(n.d)).slice(0,s)){if(e=e.slice(s-3,s+1),i==s&&e=="4999"){if(L(o,r+1,0),o.times(o).eq(a)){n=o;break}}else if(e!="9999")break;s+=4}return q=!0,L(n,r)};S.times=S.mul=function(t){var e,r,n,i,o,s,a,f,h,C=this,R=C.constructor,k=C.d,A=(t=new R(t)).d;if(!C.s||!t.s)return new R(0);for(t.s*=C.s,r=C.e+t.e,f=k.length,h=A.length,f<h&&(o=k,k=A,A=o,s=f,f=h,h=s),o=[],s=f+h,n=s;n--;)o.push(0);for(n=h;--n>=0;){for(e=0,i=f+n;i>n;)a=o[i]+A[n]*k[i-n-1]+e,o[i--]=a%J|0,e=a/J|0;o[i]=(o[i]+e)%J|0}for(;!o[--s];)o.pop();return e?++r:o.shift(),t.d=o,t.e=r,q?L(t,R.precision):t};S.toDecimalPlaces=S.todp=function(t,e){var r=this,n=r.constructor;return r=new n(r),t===void 0?r:(ce(t,0,Ue),e===void 0?e=n.rounding:ce(e,0,8),L(r,t+j(r)+1,e))};S.toExponential=function(t,e){var r,n=this,i=n.constructor;return t===void 0?r=De(n,!0):(ce(t,0,Ue),e===void 0?e=i.rounding:ce(e,0,8),n=L(new i(n),t+1,e),r=De(n,!0,t+1)),r};S.toFixed=function(t,e){var r,n,i=this,o=i.constructor;return t===void 0?De(i):(ce(t,0,Ue),e===void 0?e=o.rounding:ce(e,0,8),n=L(new o(i),t+j(i)+1,e),r=De(n.abs(),!1,t+j(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};S.toInteger=S.toint=function(){var t=this,e=t.constructor;return L(new e(t),j(t)+1,e.rounding)};S.toNumber=function(){return+this};S.toPower=S.pow=function(t){var e,r,n,i,o,s,a=this,f=a.constructor,h=12,C=+(t=new f(t));if(!t.s)return new f(te);if(a=new f(a),!a.s){if(t.s<1)throw Error(oe+"Infinity");return a}if(a.eq(te))return a;if(n=f.precision,t.eq(te))return L(a,n);if(e=t.e,r=t.d.length-1,s=e>=r,o=a.s,s){if((r=C<0?-C:C)<=yn){for(i=new f(te),e=Math.ceil(n/N+4),q=!1;r%2&&(i=i.times(a),gn(i.d,e)),r=Ne(r/2),r!==0;)a=a.times(a),gn(a.d,e);return q=!0,t.s<0?new f(te).div(i):L(i,n)}}else if(o<0)throw Error(oe+"NaN");return o=o<0&&t.d[Math.max(e,r)]&1?-1:1,a.s=1,q=!1,i=t.times(ot(a,n+h)),q=!0,i=bn(i),i.s=o,i};S.toPrecision=function(t,e){var r,n,i=this,o=i.constructor;return t===void 0?(r=j(i),n=De(i,r<=o.toExpNeg||r>=o.toExpPos)):(ce(t,1,Ue),e===void 0?e=o.rounding:ce(e,0,8),i=L(new o(i),t,e),r=j(i),n=De(i,t<=r||r<=o.toExpNeg,t)),n};S.toSignificantDigits=S.tosd=function(t,e){var r=this,n=r.constructor;return t===void 0?(t=n.precision,e=n.rounding):(ce(t,1,Ue),e===void 0?e=n.rounding:ce(e,0,8)),L(new n(r),t,e)};S.toString=S.valueOf=S.val=S.toJSON=S[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=this,e=j(t),r=t.constructor;return De(t,e<=r.toExpNeg||e>=r.toExpPos)};he=function(){function t(n,i){var o,s=0,a=n.length;for(n=n.slice();a--;)o=n[a]*i+s,n[a]=o%J|0,s=o/J|0;return s&&n.unshift(s),n}function e(n,i,o,s){var a,f;if(o!=s)f=o>s?1:-1;else for(a=f=0;a<o;a++)if(n[a]!=i[a]){f=n[a]>i[a]?1:-1;break}return f}function r(n,i,o){for(var s=0;o--;)n[o]-=s,s=n[o]<i[o]?1:0,n[o]=s*J+n[o]-i[o];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,o,s){var a,f,h,C,R,k,A,_,O,D,ye,z,F,Y,Se,xr,se,St,Ot=n.constructor,Yo=n.s==i.s?1:-1,le=n.d,B=i.d;if(!n.s)return new Ot(n);if(!i.s)throw Error(oe+"Division by zero");for(f=n.e-i.e,se=B.length,Se=le.length,A=new Ot(Yo),_=A.d=[],h=0;B[h]==(le[h]||0);)++h;if(B[h]>(le[h]||0)&&--f,o==null?z=o=Ot.precision:s?z=o+(j(n)-j(i))+1:z=o,z<0)return new Ot(0);if(z=z/N+2|0,h=0,se==1)for(C=0,B=B[0],z++;(h<Se||C)&&z--;h++)F=C*J+(le[h]||0),_[h]=F/B|0,C=F%B|0;else{for(C=J/(B[0]+1)|0,C>1&&(B=t(B,C),le=t(le,C),se=B.length,Se=le.length),Y=se,O=le.slice(0,se),D=O.length;D<se;)O[D++]=0;St=B.slice(),St.unshift(0),xr=B[0],B[1]>=J/2&&++xr;do C=0,a=e(B,O,se,D),a<0?(ye=O[0],se!=D&&(ye=ye*J+(O[1]||0)),C=ye/xr|0,C>1?(C>=J&&(C=J-1),R=t(B,C),k=R.length,D=O.length,a=e(R,O,k,D),a==1&&(C--,r(R,se<k?St:B,k))):(C==0&&(a=C=1),R=B.slice()),k=R.length,k<D&&R.unshift(0),r(O,R,D),a==-1&&(D=O.length,a=e(B,O,se,D),a<1&&(C++,r(O,se<D?St:B,D))),D=O.length):a===0&&(C++,O=[0]),_[h++]=C,a&&O[0]?O[D++]=le[Y]||0:(O=[le[Y]],D=1);while((Y++<Se||O[0]!==void 0)&&z--)}return _[0]||_.shift(),A.e=f,L(A,s?o+j(A)+1:o)}}();Cr=xn(ps);te=new Cr(1);Mt=Cr});var v,be,l=ie(()=>{"use strict";En();v=class extends Mt{static isDecimal(e){return e instanceof Mt}static random(e=20){{let n=globalThis.crypto.getRandomValues(new Uint8Array(e)).reduce((i,o)=>i+o,"");return new Mt(`0.${n.slice(0,e)}`)}}},be=v});function xs(){return!1}function kr(){return{dev:0,ino:0,mode:0,nlink:0,uid:0,gid:0,rdev:0,size:0,blksize:0,blocks:0,atimeMs:0,mtimeMs:0,ctimeMs:0,birthtimeMs:0,atime:new Date,mtime:new Date,ctime:new Date,birthtime:new Date}}function Es(){return kr()}function Ps(){return[]}function vs(t){t(null,[])}function Ts(){return""}function Cs(){return""}function Rs(){}function As(){}function Ss(){}function Os(){}function ks(){}function Ds(){}function Is(){}function Ms(){}function _s(){return{close:()=>{},on:()=>{},removeAllListeners:()=>{}}}function Ls(t,e){e(null,kr())}var Fs,Us,Nn,qn=ie(()=>{"use strict";u();c();m();p();d();l();Fs={},Us={existsSync:xs,lstatSync:kr,stat:Ls,statSync:Es,readdirSync:Ps,readdir:vs,readlinkSync:Ts,realpathSync:Cs,chmodSync:Rs,renameSync:As,mkdirSync:Ss,rmdirSync:Os,rmSync:ks,unlinkSync:Ds,watchFile:Is,unwatchFile:Ms,watch:_s,promises:Fs},Nn=Us});function Ns(...t){return t.join("/")}function qs(...t){return t.join("/")}function Bs(t){let e=Bn(t),r=Vn(t),[n,i]=e.split(".");return{root:"/",dir:r,base:e,ext:i,name:n}}function Bn(t){let e=t.split("/");return e[e.length-1]}function Vn(t){return t.split("/").slice(0,-1).join("/")}function js(t){let e=t.split("/").filter(i=>i!==""&&i!=="."),r=[];for(let i of e)i===".."?r.pop():r.push(i);let n=r.join("/");return t.startsWith("/")?"/"+n:n}var jn,Vs,$s,Qs,Ut,$n=ie(()=>{"use strict";u();c();m();p();d();l();jn="/",Vs=":";$s={sep:jn},Qs={basename:Bn,delimiter:Vs,dirname:Vn,join:qs,normalize:js,parse:Bs,posix:$s,resolve:Ns,sep:jn},Ut=Qs});var Qn=Fe((lm,Js)=>{Js.exports={name:"@prisma/internals",version:"6.14.0",description:"This package is intended for Prisma's internal use",main:"dist/index.js",types:"dist/index.d.ts",repository:{type:"git",url:"https://github.com/prisma/prisma.git",directory:"packages/internals"},homepage:"https://www.prisma.io",author:"Tim Suchanek <<EMAIL>>",bugs:"https://github.com/prisma/prisma/issues",license:"Apache-2.0",scripts:{dev:"DEV=true tsx helpers/build.ts",build:"tsx helpers/build.ts",test:"dotenv -e ../../.db.env -- jest --silent",prepublishOnly:"pnpm run build"},files:["README.md","dist","!**/libquery_engine*","!dist/get-generators/engines/*","scripts"],devDependencies:{"@babel/helper-validator-identifier":"7.25.9","@opentelemetry/api":"1.9.0","@swc/core":"1.11.5","@swc/jest":"0.2.37","@types/babel__helper-validator-identifier":"7.15.2","@types/jest":"29.5.14","@types/node":"18.19.76","@types/resolve":"1.20.6",archiver:"6.0.2","checkpoint-client":"1.1.33","cli-truncate":"4.0.0",dotenv:"16.5.0",empathic:"2.0.0",esbuild:"0.25.5","escape-string-regexp":"5.0.0",execa:"5.1.1","fast-glob":"3.3.3","find-up":"7.0.0","fp-ts":"2.16.9","fs-extra":"11.3.0","fs-jetpack":"5.1.0","global-dirs":"4.0.0",globby:"11.1.0","identifier-regex":"1.0.0","indent-string":"4.0.0","is-windows":"1.0.2","is-wsl":"3.1.0",jest:"29.7.0","jest-junit":"16.0.0",kleur:"4.1.5","mock-stdin":"1.0.0","new-github-issue-url":"0.2.1","node-fetch":"3.3.2","npm-packlist":"5.1.3",open:"7.4.2","p-map":"4.0.0",resolve:"1.22.10","string-width":"7.2.0","strip-ansi":"6.0.1","strip-indent":"4.0.0","temp-dir":"2.0.0",tempy:"1.0.1","terminal-link":"4.0.0",tmp:"0.2.3","ts-node":"10.9.2","ts-pattern":"5.6.2","ts-toolbelt":"9.6.0",typescript:"5.4.5",yarn:"1.22.22"},dependencies:{"@prisma/config":"workspace:*","@prisma/debug":"workspace:*","@prisma/dmmf":"workspace:*","@prisma/driver-adapter-utils":"workspace:*","@prisma/engines":"workspace:*","@prisma/fetch-engine":"workspace:*","@prisma/generator":"workspace:*","@prisma/generator-helper":"workspace:*","@prisma/get-platform":"workspace:*","@prisma/prisma-schema-wasm":"6.14.0-25.717184b7b35ea05dfa71a3236b7af656013e1e49","@prisma/schema-engine-wasm":"6.14.0-25.717184b7b35ea05dfa71a3236b7af656013e1e49","@prisma/schema-files-loader":"workspace:*",arg:"5.0.2",prompts:"2.4.2"},peerDependencies:{typescript:">=5.1.0"},peerDependenciesMeta:{typescript:{optional:!0}},sideEffects:!1}});var Hn=Fe((xp,Kn)=>{"use strict";u();c();m();p();d();l();Kn.exports=(t,e=1,r)=>{if(r={indent:" ",includeEmptyLines:!1,...r},typeof t!="string")throw new TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof t}\``);if(typeof e!="number")throw new TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof e}\``);if(typeof r.indent!="string")throw new TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof r.indent}\``);if(e===0)return t;let n=r.includeEmptyLines?/^/gm:/^(?!\s*$)/gm;return t.replace(n,r.indent.repeat(e))}});var Xn=Fe((_p,Yn)=>{"use strict";u();c();m();p();d();l();Yn.exports=({onlyFirst:t=!1}={})=>{let e=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(e,t?void 0:"g")}});var ei=Fe((Vp,Zn)=>{"use strict";u();c();m();p();d();l();var ta=Xn();Zn.exports=t=>typeof t=="string"?t.replace(ta(),""):t});var Br=Fe((zy,oi)=>{"use strict";u();c();m();p();d();l();oi.exports=function(){function t(e,r,n,i,o){return e<r||n<r?e>n?n+1:e+1:i===o?r:r+1}return function(e,r){if(e===r)return 0;if(e.length>r.length){var n=e;e=r,r=n}for(var i=e.length,o=r.length;i>0&&e.charCodeAt(i-1)===r.charCodeAt(o-1);)i--,o--;for(var s=0;s<i&&e.charCodeAt(s)===r.charCodeAt(s);)s++;if(i-=s,o-=s,i===0||o<3)return o;var a=0,f,h,C,R,k,A,_,O,D,ye,z,F,Y=[];for(f=0;f<i;f++)Y.push(f+1),Y.push(e.charCodeAt(s+f));for(var Se=Y.length-1;a<o-3;)for(D=r.charCodeAt(s+(h=a)),ye=r.charCodeAt(s+(C=a+1)),z=r.charCodeAt(s+(R=a+2)),F=r.charCodeAt(s+(k=a+3)),A=a+=4,f=0;f<Se;f+=2)_=Y[f],O=Y[f+1],h=t(_,h,C,D,O),C=t(h,C,R,ye,O),R=t(C,R,k,z,O),A=t(R,k,A,F,O),Y[f]=A,k=R,R=C,C=h,h=_;for(;a<o;)for(D=r.charCodeAt(s+(h=a)),A=++a,f=0;f<Se;f+=2)_=Y[f],Y[f]=A=t(_,h,A,D,Y[f+1]),h=_;return A}}()});var ci=ie(()=>{"use strict";u();c();m();p();d();l()});var mi=ie(()=>{"use strict";u();c();m();p();d();l()});var Li=Fe((YP,Ga)=>{Ga.exports={name:"@prisma/engines-version",version:"6.14.0-25.717184b7b35ea05dfa71a3236b7af656013e1e49",main:"index.js",types:"index.d.ts",license:"Apache-2.0",author:"Tim Suchanek <<EMAIL>>",prisma:{enginesVersion:"717184b7b35ea05dfa71a3236b7af656013e1e49"},repository:{type:"git",url:"https://github.com/prisma/engines-wrapper.git",directory:"packages/engines-version"},devDependencies:{"@types/node":"18.19.76",typescript:"4.9.5"},files:["index.js","index.d.ts"],scripts:{build:"tsc -d"}}});var sr,Fi=ie(()=>{"use strict";u();c();m();p();d();l();sr=class{events={};on(e,r){return this.events[e]||(this.events[e]=[]),this.events[e].push(r),this}emit(e,...r){return this.events[e]?(this.events[e].forEach(n=>{n(...r)}),!0):!1}}});var eu={};nt(eu,{DMMF:()=>pt,Debug:()=>G,Decimal:()=>be,Extensions:()=>Rr,MetricsClient:()=>Ye,PrismaClientInitializationError:()=>I,PrismaClientKnownRequestError:()=>Z,PrismaClientRustPanicError:()=>xe,PrismaClientUnknownRequestError:()=>Q,PrismaClientValidationError:()=>K,Public:()=>Ar,Sql:()=>ee,createParam:()=>Ai,defineDmmfProperty:()=>Mi,deserializeJsonResponse:()=>et,deserializeRawResult:()=>br,dmmfToRuntimeDataModel:()=>ii,empty:()=>Ni,getPrismaClient:()=>Ko,getRuntime:()=>Re,join:()=>Ui,makeStrictEnum:()=>Ho,makeTypedQueryFactory:()=>_i,objectEnumValues:()=>zt,raw:()=>Hr,serializeJsonQuery:()=>nr,skip:()=>rr,sqltag:()=>zr,warnEnvConflicts:()=>void 0,warnOnce:()=>ut});module.exports=ns(eu);u();c();m();p();d();l();var Rr={};nt(Rr,{defineExtension:()=>Pn,getExtensionContext:()=>vn});u();c();m();p();d();l();u();c();m();p();d();l();function Pn(t){return typeof t=="function"?t:e=>e.$extends(t)}u();c();m();p();d();l();function vn(t){return t}var Ar={};nt(Ar,{validator:()=>Tn});u();c();m();p();d();l();u();c();m();p();d();l();function Tn(...t){return e=>e}u();c();m();p();d();l();u();c();m();p();d();l();u();c();m();p();d();l();var Sr,Cn,Rn,An,Sn=!0;typeof g<"u"&&({FORCE_COLOR:Sr,NODE_DISABLE_COLORS:Cn,NO_COLOR:Rn,TERM:An}=g.env||{},Sn=g.stdout&&g.stdout.isTTY);var gs={enabled:!Cn&&Rn==null&&An!=="dumb"&&(Sr!=null&&Sr!=="0"||Sn)};function U(t,e){let r=new RegExp(`\\x1b\\[${e}m`,"g"),n=`\x1B[${t}m`,i=`\x1B[${e}m`;return function(o){return!gs.enabled||o==null?o:n+(~(""+o).indexOf(i)?o.replace(r,i+n):o)+i}}var Xu=U(0,0),_t=U(1,22),Lt=U(2,22),Zu=U(3,23),On=U(4,24),ec=U(7,27),tc=U(8,28),rc=U(9,29),nc=U(30,39),qe=U(31,39),kn=U(32,39),Dn=U(33,39),In=U(34,39),ic=U(35,39),Mn=U(36,39),oc=U(37,39),_n=U(90,39),sc=U(90,39),ac=U(40,49),lc=U(41,49),uc=U(42,49),cc=U(43,49),mc=U(44,49),pc=U(45,49),dc=U(46,49),fc=U(47,49);u();c();m();p();d();l();var ys=100,Ln=["green","yellow","blue","magenta","cyan","red"],Ft=[],Fn=Date.now(),hs=0,Or=typeof g<"u"?g.env:{};globalThis.DEBUG??=Or.DEBUG??"";globalThis.DEBUG_COLORS??=Or.DEBUG_COLORS?Or.DEBUG_COLORS==="true":!0;var st={enable(t){typeof t=="string"&&(globalThis.DEBUG=t)},disable(){let t=globalThis.DEBUG;return globalThis.DEBUG="",t},enabled(t){let e=globalThis.DEBUG.split(",").map(i=>i.replace(/[.+?^${}()|[\]\\]/g,"\\$&")),r=e.some(i=>i===""||i[0]==="-"?!1:t.match(RegExp(i.split("*").join(".*")+"$"))),n=e.some(i=>i===""||i[0]!=="-"?!1:t.match(RegExp(i.slice(1).split("*").join(".*")+"$")));return r&&!n},log:(...t)=>{let[e,r,...n]=t;(console.warn??console.log)(`${e} ${r}`,...n)},formatters:{}};function bs(t){let e={color:Ln[hs++%Ln.length],enabled:st.enabled(t),namespace:t,log:st.log,extend:()=>{}},r=(...n)=>{let{enabled:i,namespace:o,color:s,log:a}=e;if(n.length!==0&&Ft.push([o,...n]),Ft.length>ys&&Ft.shift(),st.enabled(o)||i){let f=n.map(C=>typeof C=="string"?C:ws(C)),h=`+${Date.now()-Fn}ms`;Fn=Date.now(),a(o,...f,h)}};return new Proxy(r,{get:(n,i)=>e[i],set:(n,i,o)=>e[i]=o})}var G=new Proxy(bs,{get:(t,e)=>st[e],set:(t,e,r)=>st[e]=r});function ws(t,e=2){let r=new Set;return JSON.stringify(t,(n,i)=>{if(typeof i=="object"&&i!==null){if(r.has(i))return"[Circular *]";r.add(i)}else if(typeof i=="bigint")return i.toString();return i},e)}function Un(){Ft.length=0}u();c();m();p();d();l();u();c();m();p();d();l();var Dr=["darwin","darwin-arm64","debian-openssl-1.0.x","debian-openssl-1.1.x","debian-openssl-3.0.x","rhel-openssl-1.0.x","rhel-openssl-1.1.x","rhel-openssl-3.0.x","linux-arm64-openssl-1.1.x","linux-arm64-openssl-1.0.x","linux-arm64-openssl-3.0.x","linux-arm-openssl-1.1.x","linux-arm-openssl-1.0.x","linux-arm-openssl-3.0.x","linux-musl","linux-musl-openssl-3.0.x","linux-musl-arm64-openssl-1.1.x","linux-musl-arm64-openssl-3.0.x","linux-nixos","linux-static-x64","linux-static-arm64","windows","freebsd11","freebsd12","freebsd13","freebsd14","freebsd15","openbsd","netbsd","arm"];u();c();m();p();d();l();var Gs=Qn(),Ir=Gs.version;u();c();m();p();d();l();function Be(t){let e=Ws();return e||(t?.config.engineType==="library"?"library":t?.config.engineType==="binary"?"binary":t?.config.engineType==="client"?"client":Ks(t))}function Ws(){let t=g.env.PRISMA_CLIENT_ENGINE_TYPE;return t==="library"?"library":t==="binary"?"binary":t==="client"?"client":void 0}function Ks(t){return t?.previewFeatures.includes("queryCompiler")?"client":"library"}u();c();m();p();d();l();u();c();m();p();d();l();u();c();m();p();d();l();u();c();m();p();d();l();function Mr(t){return t.name==="DriverAdapterError"&&typeof t.cause=="object"}u();c();m();p();d();l();function Nt(t){return{ok:!0,value:t,map(e){return Nt(e(t))},flatMap(e){return e(t)}}}function Ie(t){return{ok:!1,error:t,map(){return Ie(t)},flatMap(){return Ie(t)}}}var Jn=G("driver-adapter-utils"),_r=class{registeredErrors=[];consumeError(e){return this.registeredErrors[e]}registerNewError(e){let r=0;for(;this.registeredErrors[r]!==void 0;)r++;return this.registeredErrors[r]={error:e},r}};var qt=(t,e=new _r)=>{let r={adapterName:t.adapterName,errorRegistry:e,queryRaw:we(e,t.queryRaw.bind(t)),executeRaw:we(e,t.executeRaw.bind(t)),executeScript:we(e,t.executeScript.bind(t)),dispose:we(e,t.dispose.bind(t)),provider:t.provider,startTransaction:async(...n)=>(await we(e,t.startTransaction.bind(t))(...n)).map(o=>Hs(e,o))};return t.getConnectionInfo&&(r.getConnectionInfo=zs(e,t.getConnectionInfo.bind(t))),r},Hs=(t,e)=>({adapterName:e.adapterName,provider:e.provider,options:e.options,queryRaw:we(t,e.queryRaw.bind(e)),executeRaw:we(t,e.executeRaw.bind(e)),commit:we(t,e.commit.bind(e)),rollback:we(t,e.rollback.bind(e))});function we(t,e){return async(...r)=>{try{return Nt(await e(...r))}catch(n){if(Jn("[error@wrapAsync]",n),Mr(n))return Ie(n.cause);let i=t.registerNewError(n);return Ie({kind:"GenericJs",id:i})}}}function zs(t,e){return(...r)=>{try{return Nt(e(...r))}catch(n){if(Jn("[error@wrapSync]",n),Mr(n))return Ie(n.cause);let i=t.registerNewError(n);return Ie({kind:"GenericJs",id:i})}}}u();c();m();p();d();l();var Gn="prisma+postgres",Wn=`${Gn}:`;function Lr(t){return t?.toString().startsWith(`${Wn}//`)??!1}var lt={};nt(lt,{error:()=>Zs,info:()=>Xs,log:()=>Ys,query:()=>ea,should:()=>zn,tags:()=>at,warn:()=>Fr});u();c();m();p();d();l();var at={error:qe("prisma:error"),warn:Dn("prisma:warn"),info:Mn("prisma:info"),query:In("prisma:query")},zn={warn:()=>!g.env.PRISMA_DISABLE_WARNINGS};function Ys(...t){console.log(...t)}function Fr(t,...e){zn.warn()&&console.warn(`${at.warn} ${t}`,...e)}function Xs(t,...e){console.info(`${at.info} ${t}`,...e)}function Zs(t,...e){console.error(`${at.error} ${t}`,...e)}function ea(t,...e){console.log(`${at.query} ${t}`,...e)}u();c();m();p();d();l();function Bt(t,e){if(!t)throw new Error(`${e}. This should never happen. If you see this error, please, open an issue at https://pris.ly/prisma-prisma-bug-report`)}u();c();m();p();d();l();function Me(t,e){throw new Error(e)}u();c();m();p();d();l();function Ur(t,e){return Object.prototype.hasOwnProperty.call(t,e)}u();c();m();p();d();l();function Vt(t,e){let r={};for(let n of Object.keys(t))r[n]=e(t[n],n);return r}u();c();m();p();d();l();function Nr(t,e){if(t.length===0)return;let r=t[0];for(let n=1;n<t.length;n++)e(r,t[n])<0&&(r=t[n]);return r}u();c();m();p();d();l();function re(t,e){Object.defineProperty(t,"name",{value:e,configurable:!0})}u();c();m();p();d();l();var ti=new Set,ut=(t,e,...r)=>{ti.has(t)||(ti.add(t),Fr(e,...r))};var I=class t extends Error{clientVersion;errorCode;retryable;constructor(e,r,n){super(e),this.name="PrismaClientInitializationError",this.clientVersion=r,this.errorCode=n,Error.captureStackTrace(t)}get[Symbol.toStringTag](){return"PrismaClientInitializationError"}};re(I,"PrismaClientInitializationError");u();c();m();p();d();l();var Z=class extends Error{code;meta;clientVersion;batchRequestIdx;constructor(e,{code:r,clientVersion:n,meta:i,batchRequestIdx:o}){super(e),this.name="PrismaClientKnownRequestError",this.code=r,this.clientVersion=n,this.meta=i,Object.defineProperty(this,"batchRequestIdx",{value:o,enumerable:!1,writable:!0})}get[Symbol.toStringTag](){return"PrismaClientKnownRequestError"}};re(Z,"PrismaClientKnownRequestError");u();c();m();p();d();l();var xe=class extends Error{clientVersion;constructor(e,r){super(e),this.name="PrismaClientRustPanicError",this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientRustPanicError"}};re(xe,"PrismaClientRustPanicError");u();c();m();p();d();l();var Q=class extends Error{clientVersion;batchRequestIdx;constructor(e,{clientVersion:r,batchRequestIdx:n}){super(e),this.name="PrismaClientUnknownRequestError",this.clientVersion=r,Object.defineProperty(this,"batchRequestIdx",{value:n,writable:!0,enumerable:!1})}get[Symbol.toStringTag](){return"PrismaClientUnknownRequestError"}};re(Q,"PrismaClientUnknownRequestError");u();c();m();p();d();l();var K=class extends Error{name="PrismaClientValidationError";clientVersion;constructor(e,{clientVersion:r}){super(e),this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientValidationError"}};re(K,"PrismaClientValidationError");u();c();m();p();d();l();u();c();m();p();d();l();u();c();m();p();d();l();var me=class{_map=new Map;get(e){return this._map.get(e)?.value}set(e,r){this._map.set(e,{value:r})}getOrCreate(e,r){let n=this._map.get(e);if(n)return n.value;let i=r();return this.set(e,i),i}};u();c();m();p();d();l();function ve(t){return t.substring(0,1).toLowerCase()+t.substring(1)}u();c();m();p();d();l();function ni(t,e){let r={};for(let n of t){let i=n[e];r[i]=n}return r}u();c();m();p();d();l();function ct(t){let e;return{get(){return e||(e={value:t()}),e.value}}}u();c();m();p();d();l();function ii(t){return{models:qr(t.models),enums:qr(t.enums),types:qr(t.types)}}function qr(t){let e={};for(let{name:r,...n}of t)e[r]=n;return e}u();c();m();p();d();l();function Ve(t){return t instanceof Date||Object.prototype.toString.call(t)==="[object Date]"}function jt(t){return t.toString()!=="Invalid Date"}u();c();m();p();d();l();l();function je(t){return v.isDecimal(t)?!0:t!==null&&typeof t=="object"&&typeof t.s=="number"&&typeof t.e=="number"&&typeof t.toFixed=="function"&&Array.isArray(t.d)}u();c();m();p();d();l();u();c();m();p();d();l();var pt={};nt(pt,{ModelAction:()=>mt,datamodelEnumToSchemaEnum:()=>ra});u();c();m();p();d();l();u();c();m();p();d();l();function ra(t){return{name:t.name,values:t.values.map(e=>e.name)}}u();c();m();p();d();l();var mt=(F=>(F.findUnique="findUnique",F.findUniqueOrThrow="findUniqueOrThrow",F.findFirst="findFirst",F.findFirstOrThrow="findFirstOrThrow",F.findMany="findMany",F.create="create",F.createMany="createMany",F.createManyAndReturn="createManyAndReturn",F.update="update",F.updateMany="updateMany",F.updateManyAndReturn="updateManyAndReturn",F.upsert="upsert",F.delete="delete",F.deleteMany="deleteMany",F.groupBy="groupBy",F.count="count",F.aggregate="aggregate",F.findRaw="findRaw",F.aggregateRaw="aggregateRaw",F))(mt||{});var na=it(Hn());var ia={red:qe,gray:_n,dim:Lt,bold:_t,underline:On,highlightSource:t=>t.highlight()},oa={red:t=>t,gray:t=>t,dim:t=>t,bold:t=>t,underline:t=>t,highlightSource:t=>t};function sa({message:t,originalMethod:e,isPanic:r,callArguments:n}){return{functionName:`prisma.${e}()`,message:t,isPanic:r??!1,callArguments:n}}function aa({functionName:t,location:e,message:r,isPanic:n,contextLines:i,callArguments:o},s){let a=[""],f=e?" in":":";if(n?(a.push(s.red(`Oops, an unknown error occurred! This is ${s.bold("on us")}, you did nothing wrong.`)),a.push(s.red(`It occurred in the ${s.bold(`\`${t}\``)} invocation${f}`))):a.push(s.red(`Invalid ${s.bold(`\`${t}\``)} invocation${f}`)),e&&a.push(s.underline(la(e))),i){a.push("");let h=[i.toString()];o&&(h.push(o),h.push(s.dim(")"))),a.push(h.join("")),o&&a.push("")}else a.push(""),o&&a.push(o),a.push("");return a.push(r),a.join(`
`)}function la(t){let e=[t.fileName];return t.lineNumber&&e.push(String(t.lineNumber)),t.columnNumber&&e.push(String(t.columnNumber)),e.join(":")}function $t(t){let e=t.showColors?ia:oa,r;return typeof $getTemplateParameters<"u"?r=$getTemplateParameters(t,e):r=sa(t),aa(r,e)}u();c();m();p();d();l();var di=it(Br());u();c();m();p();d();l();function li(t,e,r){let n=ui(t),i=ua(n),o=ma(i);o?Qt(o,e,r):e.addErrorMessage(()=>"Unknown error")}function ui(t){return t.errors.flatMap(e=>e.kind==="Union"?ui(e):[e])}function ua(t){let e=new Map,r=[];for(let n of t){if(n.kind!=="InvalidArgumentType"){r.push(n);continue}let i=`${n.selectionPath.join(".")}:${n.argumentPath.join(".")}`,o=e.get(i);o?e.set(i,{...n,argument:{...n.argument,typeNames:ca(o.argument.typeNames,n.argument.typeNames)}}):e.set(i,n)}return r.push(...e.values()),r}function ca(t,e){return[...new Set(t.concat(e))]}function ma(t){return Nr(t,(e,r)=>{let n=si(e),i=si(r);return n!==i?n-i:ai(e)-ai(r)})}function si(t){let e=0;return Array.isArray(t.selectionPath)&&(e+=t.selectionPath.length),Array.isArray(t.argumentPath)&&(e+=t.argumentPath.length),e}function ai(t){switch(t.kind){case"InvalidArgumentValue":case"ValueTooLarge":return 20;case"InvalidArgumentType":return 10;case"RequiredArgumentMissing":return-10;default:return 0}}u();c();m();p();d();l();var ne=class{constructor(e,r){this.name=e;this.value=r}isRequired=!1;makeRequired(){return this.isRequired=!0,this}write(e){let{colors:{green:r}}=e.context;e.addMarginSymbol(r(this.isRequired?"+":"?")),e.write(r(this.name)),this.isRequired||e.write(r("?")),e.write(r(": ")),typeof this.value=="string"?e.write(r(this.value)):e.write(this.value)}};u();c();m();p();d();l();u();c();m();p();d();l();mi();u();c();m();p();d();l();var $e=class{constructor(e=0,r){this.context=r;this.currentIndent=e}lines=[];currentLine="";currentIndent=0;marginSymbol;afterNextNewLineCallback;write(e){return typeof e=="string"?this.currentLine+=e:e.write(this),this}writeJoined(e,r,n=(i,o)=>o.write(i)){let i=r.length-1;for(let o=0;o<r.length;o++)n(r[o],this),o!==i&&this.write(e);return this}writeLine(e){return this.write(e).newLine()}newLine(){this.lines.push(this.indentedCurrentLine()),this.currentLine="",this.marginSymbol=void 0;let e=this.afterNextNewLineCallback;return this.afterNextNewLineCallback=void 0,e?.(),this}withIndent(e){return this.indent(),e(this),this.unindent(),this}afterNextNewline(e){return this.afterNextNewLineCallback=e,this}indent(){return this.currentIndent++,this}unindent(){return this.currentIndent>0&&this.currentIndent--,this}addMarginSymbol(e){return this.marginSymbol=e,this}toString(){return this.lines.concat(this.indentedCurrentLine()).join(`
`)}getCurrentLineLength(){return this.currentLine.length}indentedCurrentLine(){let e=this.currentLine.padStart(this.currentLine.length+2*this.currentIndent);return this.marginSymbol?this.marginSymbol+e.slice(1):e}};ci();u();c();m();p();d();l();u();c();m();p();d();l();var Jt=class{constructor(e){this.value=e}write(e){e.write(this.value)}markAsError(){this.value.markAsError()}};u();c();m();p();d();l();var Gt=t=>t,Wt={bold:Gt,red:Gt,green:Gt,dim:Gt,enabled:!1},pi={bold:_t,red:qe,green:kn,dim:Lt,enabled:!0},Qe={write(t){t.writeLine(",")}};u();c();m();p();d();l();var pe=class{constructor(e){this.contents=e}isUnderlined=!1;color=e=>e;underline(){return this.isUnderlined=!0,this}setColor(e){return this.color=e,this}write(e){let r=e.getCurrentLineLength();e.write(this.color(this.contents)),this.isUnderlined&&e.afterNextNewline(()=>{e.write(" ".repeat(r)).writeLine(this.color("~".repeat(this.contents.length)))})}};u();c();m();p();d();l();var Te=class{hasError=!1;markAsError(){return this.hasError=!0,this}};var Je=class extends Te{items=[];addItem(e){return this.items.push(new Jt(e)),this}getField(e){return this.items[e]}getPrintWidth(){return this.items.length===0?2:Math.max(...this.items.map(r=>r.value.getPrintWidth()))+2}write(e){if(this.items.length===0){this.writeEmpty(e);return}this.writeWithItems(e)}writeEmpty(e){let r=new pe("[]");this.hasError&&r.setColor(e.context.colors.red).underline(),e.write(r)}writeWithItems(e){let{colors:r}=e.context;e.writeLine("[").withIndent(()=>e.writeJoined(Qe,this.items).newLine()).write("]"),this.hasError&&e.afterNextNewline(()=>{e.writeLine(r.red("~".repeat(this.getPrintWidth())))})}asObject(){}};var Ge=class t extends Te{fields={};suggestions=[];addField(e){this.fields[e.name]=e}addSuggestion(e){this.suggestions.push(e)}getField(e){return this.fields[e]}getDeepField(e){let[r,...n]=e,i=this.getField(r);if(!i)return;let o=i;for(let s of n){let a;if(o.value instanceof t?a=o.value.getField(s):o.value instanceof Je&&(a=o.value.getField(Number(s))),!a)return;o=a}return o}getDeepFieldValue(e){return e.length===0?this:this.getDeepField(e)?.value}hasField(e){return!!this.getField(e)}removeAllFields(){this.fields={}}removeField(e){delete this.fields[e]}getFields(){return this.fields}isEmpty(){return Object.keys(this.fields).length===0}getFieldValue(e){return this.getField(e)?.value}getDeepSubSelectionValue(e){let r=this;for(let n of e){if(!(r instanceof t))return;let i=r.getSubSelectionValue(n);if(!i)return;r=i}return r}getDeepSelectionParent(e){let r=this.getSelectionParent();if(!r)return;let n=r;for(let i of e){let o=n.value.getFieldValue(i);if(!o||!(o instanceof t))return;let s=o.getSelectionParent();if(!s)return;n=s}return n}getSelectionParent(){let e=this.getField("select")?.value.asObject();if(e)return{kind:"select",value:e};let r=this.getField("include")?.value.asObject();if(r)return{kind:"include",value:r}}getSubSelectionValue(e){return this.getSelectionParent()?.value.fields[e].value}getPrintWidth(){let e=Object.values(this.fields);return e.length==0?2:Math.max(...e.map(n=>n.getPrintWidth()))+2}write(e){let r=Object.values(this.fields);if(r.length===0&&this.suggestions.length===0){this.writeEmpty(e);return}this.writeWithContents(e,r)}asObject(){return this}writeEmpty(e){let r=new pe("{}");this.hasError&&r.setColor(e.context.colors.red).underline(),e.write(r)}writeWithContents(e,r){e.writeLine("{").withIndent(()=>{e.writeJoined(Qe,[...r,...this.suggestions]).newLine()}),e.write("}"),this.hasError&&e.afterNextNewline(()=>{e.writeLine(e.context.colors.red("~".repeat(this.getPrintWidth())))})}};u();c();m();p();d();l();var W=class extends Te{constructor(r){super();this.text=r}getPrintWidth(){return this.text.length}write(r){let n=new pe(this.text);this.hasError&&n.underline().setColor(r.context.colors.red),r.write(n)}asObject(){}};u();c();m();p();d();l();var dt=class{fields=[];addField(e,r){return this.fields.push({write(n){let{green:i,dim:o}=n.context.colors;n.write(i(o(`${e}: ${r}`))).addMarginSymbol(i(o("+")))}}),this}write(e){let{colors:{green:r}}=e.context;e.writeLine(r("{")).withIndent(()=>{e.writeJoined(Qe,this.fields).newLine()}).write(r("}")).addMarginSymbol(r("+"))}};function Qt(t,e,r){switch(t.kind){case"MutuallyExclusiveFields":pa(t,e);break;case"IncludeOnScalar":da(t,e);break;case"EmptySelection":fa(t,e,r);break;case"UnknownSelectionField":ba(t,e);break;case"InvalidSelectionValue":wa(t,e);break;case"UnknownArgument":xa(t,e);break;case"UnknownInputField":Ea(t,e);break;case"RequiredArgumentMissing":Pa(t,e);break;case"InvalidArgumentType":va(t,e);break;case"InvalidArgumentValue":Ta(t,e);break;case"ValueTooLarge":Ca(t,e);break;case"SomeFieldsMissing":Ra(t,e);break;case"TooManyFieldsGiven":Aa(t,e);break;case"Union":li(t,e,r);break;default:throw new Error("not implemented: "+t.kind)}}function pa(t,e){let r=e.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject();r&&(r.getField(t.firstField)?.markAsError(),r.getField(t.secondField)?.markAsError()),e.addErrorMessage(n=>`Please ${n.bold("either")} use ${n.green(`\`${t.firstField}\``)} or ${n.green(`\`${t.secondField}\``)}, but ${n.red("not both")} at the same time.`)}function da(t,e){let[r,n]=We(t.selectionPath),i=t.outputType,o=e.arguments.getDeepSelectionParent(r)?.value;if(o&&(o.getField(n)?.markAsError(),i))for(let s of i.fields)s.isRelation&&o.addSuggestion(new ne(s.name,"true"));e.addErrorMessage(s=>{let a=`Invalid scalar field ${s.red(`\`${n}\``)} for ${s.bold("include")} statement`;return i?a+=` on model ${s.bold(i.name)}. ${ft(s)}`:a+=".",a+=`
Note that ${s.bold("include")} statements only accept relation fields.`,a})}function fa(t,e,r){let n=e.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject();if(n){let i=n.getField("omit")?.value.asObject();if(i){ga(t,e,i);return}if(n.hasField("select")){ya(t,e);return}}if(r?.[ve(t.outputType.name)]){ha(t,e);return}e.addErrorMessage(()=>`Unknown field at "${t.selectionPath.join(".")} selection"`)}function ga(t,e,r){r.removeAllFields();for(let n of t.outputType.fields)r.addSuggestion(new ne(n.name,"false"));e.addErrorMessage(n=>`The ${n.red("omit")} statement includes every field of the model ${n.bold(t.outputType.name)}. At least one field must be included in the result`)}function ya(t,e){let r=t.outputType,n=e.arguments.getDeepSelectionParent(t.selectionPath)?.value,i=n?.isEmpty()??!1;n&&(n.removeAllFields(),yi(n,r)),e.addErrorMessage(o=>i?`The ${o.red("`select`")} statement for type ${o.bold(r.name)} must not be empty. ${ft(o)}`:`The ${o.red("`select`")} statement for type ${o.bold(r.name)} needs ${o.bold("at least one truthy value")}.`)}function ha(t,e){let r=new dt;for(let i of t.outputType.fields)i.isRelation||r.addField(i.name,"false");let n=new ne("omit",r).makeRequired();if(t.selectionPath.length===0)e.arguments.addSuggestion(n);else{let[i,o]=We(t.selectionPath),a=e.arguments.getDeepSelectionParent(i)?.value.asObject()?.getField(o);if(a){let f=a?.value.asObject()??new Ge;f.addSuggestion(n),a.value=f}}e.addErrorMessage(i=>`The global ${i.red("omit")} configuration excludes every field of the model ${i.bold(t.outputType.name)}. At least one field must be included in the result`)}function ba(t,e){let r=hi(t.selectionPath,e);if(r.parentKind!=="unknown"){r.field.markAsError();let n=r.parent;switch(r.parentKind){case"select":yi(n,t.outputType);break;case"include":Sa(n,t.outputType);break;case"omit":Oa(n,t.outputType);break}}e.addErrorMessage(n=>{let i=[`Unknown field ${n.red(`\`${r.fieldName}\``)}`];return r.parentKind!=="unknown"&&i.push(`for ${n.bold(r.parentKind)} statement`),i.push(`on model ${n.bold(`\`${t.outputType.name}\``)}.`),i.push(ft(n)),i.join(" ")})}function wa(t,e){let r=hi(t.selectionPath,e);r.parentKind!=="unknown"&&r.field.value.markAsError(),e.addErrorMessage(n=>`Invalid value for selection field \`${n.red(r.fieldName)}\`: ${t.underlyingError}`)}function xa(t,e){let r=t.argumentPath[0],n=e.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject();n&&(n.getField(r)?.markAsError(),ka(n,t.arguments)),e.addErrorMessage(i=>fi(i,r,t.arguments.map(o=>o.name)))}function Ea(t,e){let[r,n]=We(t.argumentPath),i=e.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject();if(i){i.getDeepField(t.argumentPath)?.markAsError();let o=i.getDeepFieldValue(r)?.asObject();o&&bi(o,t.inputType)}e.addErrorMessage(o=>fi(o,n,t.inputType.fields.map(s=>s.name)))}function fi(t,e,r){let n=[`Unknown argument \`${t.red(e)}\`.`],i=Ia(e,r);return i&&n.push(`Did you mean \`${t.green(i)}\`?`),r.length>0&&n.push(ft(t)),n.join(" ")}function Pa(t,e){let r;e.addErrorMessage(f=>r?.value instanceof W&&r.value.text==="null"?`Argument \`${f.green(o)}\` must not be ${f.red("null")}.`:`Argument \`${f.green(o)}\` is missing.`);let n=e.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject();if(!n)return;let[i,o]=We(t.argumentPath),s=new dt,a=n.getDeepFieldValue(i)?.asObject();if(a){if(r=a.getField(o),r&&a.removeField(o),t.inputTypes.length===1&&t.inputTypes[0].kind==="object"){for(let f of t.inputTypes[0].fields)s.addField(f.name,f.typeNames.join(" | "));a.addSuggestion(new ne(o,s).makeRequired())}else{let f=t.inputTypes.map(gi).join(" | ");a.addSuggestion(new ne(o,f).makeRequired())}if(t.dependentArgumentPath){n.getDeepField(t.dependentArgumentPath)?.markAsError();let[,f]=We(t.dependentArgumentPath);e.addErrorMessage(h=>`Argument \`${h.green(o)}\` is required because argument \`${h.green(f)}\` was provided.`)}}}function gi(t){return t.kind==="list"?`${gi(t.elementType)}[]`:t.name}function va(t,e){let r=t.argument.name,n=e.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject();n&&n.getDeepFieldValue(t.argumentPath)?.markAsError(),e.addErrorMessage(i=>{let o=Kt("or",t.argument.typeNames.map(s=>i.green(s)));return`Argument \`${i.bold(r)}\`: Invalid value provided. Expected ${o}, provided ${i.red(t.inferredType)}.`})}function Ta(t,e){let r=t.argument.name,n=e.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject();n&&n.getDeepFieldValue(t.argumentPath)?.markAsError(),e.addErrorMessage(i=>{let o=[`Invalid value for argument \`${i.bold(r)}\``];if(t.underlyingError&&o.push(`: ${t.underlyingError}`),o.push("."),t.argument.typeNames.length>0){let s=Kt("or",t.argument.typeNames.map(a=>i.green(a)));o.push(` Expected ${s}.`)}return o.join("")})}function Ca(t,e){let r=t.argument.name,n=e.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject(),i;if(n){let s=n.getDeepField(t.argumentPath)?.value;s?.markAsError(),s instanceof W&&(i=s.text)}e.addErrorMessage(o=>{let s=["Unable to fit value"];return i&&s.push(o.red(i)),s.push(`into a 64-bit signed integer for field \`${o.bold(r)}\``),s.join(" ")})}function Ra(t,e){let r=t.argumentPath[t.argumentPath.length-1],n=e.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject();if(n){let i=n.getDeepFieldValue(t.argumentPath)?.asObject();i&&bi(i,t.inputType)}e.addErrorMessage(i=>{let o=[`Argument \`${i.bold(r)}\` of type ${i.bold(t.inputType.name)} needs`];return t.constraints.minFieldCount===1?t.constraints.requiredFields?o.push(`${i.green("at least one of")} ${Kt("or",t.constraints.requiredFields.map(s=>`\`${i.bold(s)}\``))} arguments.`):o.push(`${i.green("at least one")} argument.`):o.push(`${i.green(`at least ${t.constraints.minFieldCount}`)} arguments.`),o.push(ft(i)),o.join(" ")})}function Aa(t,e){let r=t.argumentPath[t.argumentPath.length-1],n=e.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject(),i=[];if(n){let o=n.getDeepFieldValue(t.argumentPath)?.asObject();o&&(o.markAsError(),i=Object.keys(o.getFields()))}e.addErrorMessage(o=>{let s=[`Argument \`${o.bold(r)}\` of type ${o.bold(t.inputType.name)} needs`];return t.constraints.minFieldCount===1&&t.constraints.maxFieldCount==1?s.push(`${o.green("exactly one")} argument,`):t.constraints.maxFieldCount==1?s.push(`${o.green("at most one")} argument,`):s.push(`${o.green(`at most ${t.constraints.maxFieldCount}`)} arguments,`),s.push(`but you provided ${Kt("and",i.map(a=>o.red(a)))}. Please choose`),t.constraints.maxFieldCount===1?s.push("one."):s.push(`${t.constraints.maxFieldCount}.`),s.join(" ")})}function yi(t,e){for(let r of e.fields)t.hasField(r.name)||t.addSuggestion(new ne(r.name,"true"))}function Sa(t,e){for(let r of e.fields)r.isRelation&&!t.hasField(r.name)&&t.addSuggestion(new ne(r.name,"true"))}function Oa(t,e){for(let r of e.fields)!t.hasField(r.name)&&!r.isRelation&&t.addSuggestion(new ne(r.name,"true"))}function ka(t,e){for(let r of e)t.hasField(r.name)||t.addSuggestion(new ne(r.name,r.typeNames.join(" | ")))}function hi(t,e){let[r,n]=We(t),i=e.arguments.getDeepSubSelectionValue(r)?.asObject();if(!i)return{parentKind:"unknown",fieldName:n};let o=i.getFieldValue("select")?.asObject(),s=i.getFieldValue("include")?.asObject(),a=i.getFieldValue("omit")?.asObject(),f=o?.getField(n);return o&&f?{parentKind:"select",parent:o,field:f,fieldName:n}:(f=s?.getField(n),s&&f?{parentKind:"include",field:f,parent:s,fieldName:n}:(f=a?.getField(n),a&&f?{parentKind:"omit",field:f,parent:a,fieldName:n}:{parentKind:"unknown",fieldName:n}))}function bi(t,e){if(e.kind==="object")for(let r of e.fields)t.hasField(r.name)||t.addSuggestion(new ne(r.name,r.typeNames.join(" | ")))}function We(t){let e=[...t],r=e.pop();if(!r)throw new Error("unexpected empty path");return[e,r]}function ft({green:t,enabled:e}){return"Available options are "+(e?`listed in ${t("green")}`:"marked with ?")+"."}function Kt(t,e){if(e.length===1)return e[0];let r=[...e],n=r.pop();return`${r.join(", ")} ${t} ${n}`}var Da=3;function Ia(t,e){let r=1/0,n;for(let i of e){let o=(0,di.default)(t,i);o>Da||o<r&&(r=o,n=i)}return n}u();c();m();p();d();l();u();c();m();p();d();l();var gt=class{modelName;name;typeName;isList;isEnum;constructor(e,r,n,i,o){this.modelName=e,this.name=r,this.typeName=n,this.isList=i,this.isEnum=o}_toGraphQLInputType(){let e=this.isList?"List":"",r=this.isEnum?"Enum":"";return`${e}${r}${this.typeName}FieldRefInput<${this.modelName}>`}};function Ke(t){return t instanceof gt}u();c();m();p();d();l();var Ht=Symbol(),jr=new WeakMap,Ee=class{constructor(e){e===Ht?jr.set(this,`Prisma.${this._getName()}`):jr.set(this,`new Prisma.${this._getNamespace()}.${this._getName()}()`)}_getName(){return this.constructor.name}toString(){return jr.get(this)}},yt=class extends Ee{_getNamespace(){return"NullTypes"}},ht=class extends yt{#e};$r(ht,"DbNull");var bt=class extends yt{#e};$r(bt,"JsonNull");var wt=class extends yt{#e};$r(wt,"AnyNull");var zt={classes:{DbNull:ht,JsonNull:bt,AnyNull:wt},instances:{DbNull:new ht(Ht),JsonNull:new bt(Ht),AnyNull:new wt(Ht)}};function $r(t,e){Object.defineProperty(t,"name",{value:e,configurable:!0})}u();c();m();p();d();l();var wi=": ",Yt=class{constructor(e,r){this.name=e;this.value=r}hasError=!1;markAsError(){this.hasError=!0}getPrintWidth(){return this.name.length+this.value.getPrintWidth()+wi.length}write(e){let r=new pe(this.name);this.hasError&&r.underline().setColor(e.context.colors.red),e.write(r).write(wi).write(this.value)}};var Qr=class{arguments;errorMessages=[];constructor(e){this.arguments=e}write(e){e.write(this.arguments)}addErrorMessage(e){this.errorMessages.push(e)}renderAllMessages(e){return this.errorMessages.map(r=>r(e)).join(`
`)}};function He(t){return new Qr(xi(t))}function xi(t){let e=new Ge;for(let[r,n]of Object.entries(t)){let i=new Yt(r,Ei(n));e.addField(i)}return e}function Ei(t){if(typeof t=="string")return new W(JSON.stringify(t));if(typeof t=="number"||typeof t=="boolean")return new W(String(t));if(typeof t=="bigint")return new W(`${t}n`);if(t===null)return new W("null");if(t===void 0)return new W("undefined");if(je(t))return new W(`new Prisma.Decimal("${t.toFixed()}")`);if(t instanceof Uint8Array)return b.isBuffer(t)?new W(`Buffer.alloc(${t.byteLength})`):new W(`new Uint8Array(${t.byteLength})`);if(t instanceof Date){let e=jt(t)?t.toISOString():"Invalid Date";return new W(`new Date("${e}")`)}return t instanceof Ee?new W(`Prisma.${t._getName()}`):Ke(t)?new W(`prisma.${ve(t.modelName)}.$fields.${t.name}`):Array.isArray(t)?Ma(t):typeof t=="object"?xi(t):new W(Object.prototype.toString.call(t))}function Ma(t){let e=new Je;for(let r of t)e.addItem(Ei(r));return e}function Xt(t,e){let r=e==="pretty"?pi:Wt,n=t.renderAllMessages(r),i=new $e(0,{colors:r}).write(t).toString();return{message:n,args:i}}function Zt({args:t,errors:e,errorFormat:r,callsite:n,originalMethod:i,clientVersion:o,globalOmit:s}){let a=He(t);for(let R of e)Qt(R,a,s);let{message:f,args:h}=Xt(a,r),C=$t({message:f,callsite:n,originalMethod:i,showColors:r==="pretty",callArguments:h});throw new K(C,{clientVersion:o})}u();c();m();p();d();l();u();c();m();p();d();l();function de(t){return t.replace(/^./,e=>e.toLowerCase())}u();c();m();p();d();l();function vi(t,e,r){let n=de(r);return!e.result||!(e.result.$allModels||e.result[n])?t:_a({...t,...Pi(e.name,t,e.result.$allModels),...Pi(e.name,t,e.result[n])})}function _a(t){let e=new me,r=(n,i)=>e.getOrCreate(n,()=>i.has(n)?[n]:(i.add(n),t[n]?t[n].needs.flatMap(o=>r(o,i)):[n]));return Vt(t,n=>({...n,needs:r(n.name,new Set)}))}function Pi(t,e,r){return r?Vt(r,({needs:n,compute:i},o)=>({name:o,needs:n?Object.keys(n).filter(s=>n[s]):[],compute:La(e,o,i)})):{}}function La(t,e,r){let n=t?.[e]?.compute;return n?i=>r({...i,[e]:n(i)}):r}function Ti(t,e){if(!e)return t;let r={...t};for(let n of Object.values(e))if(t[n.name])for(let i of n.needs)r[i]=!0;return r}function Ci(t,e){if(!e)return t;let r={...t};for(let n of Object.values(e))if(!t[n.name])for(let i of n.needs)delete r[i];return r}var er=class{constructor(e,r){this.extension=e;this.previous=r}computedFieldsCache=new me;modelExtensionsCache=new me;queryCallbacksCache=new me;clientExtensions=ct(()=>this.extension.client?{...this.previous?.getAllClientExtensions(),...this.extension.client}:this.previous?.getAllClientExtensions());batchCallbacks=ct(()=>{let e=this.previous?.getAllBatchQueryCallbacks()??[],r=this.extension.query?.$__internalBatch;return r?e.concat(r):e});getAllComputedFields(e){return this.computedFieldsCache.getOrCreate(e,()=>vi(this.previous?.getAllComputedFields(e),this.extension,e))}getAllClientExtensions(){return this.clientExtensions.get()}getAllModelExtensions(e){return this.modelExtensionsCache.getOrCreate(e,()=>{let r=de(e);return!this.extension.model||!(this.extension.model[r]||this.extension.model.$allModels)?this.previous?.getAllModelExtensions(e):{...this.previous?.getAllModelExtensions(e),...this.extension.model.$allModels,...this.extension.model[r]}})}getAllQueryCallbacks(e,r){return this.queryCallbacksCache.getOrCreate(`${e}:${r}`,()=>{let n=this.previous?.getAllQueryCallbacks(e,r)??[],i=[],o=this.extension.query;return!o||!(o[e]||o.$allModels||o[r]||o.$allOperations)?n:(o[e]!==void 0&&(o[e][r]!==void 0&&i.push(o[e][r]),o[e].$allOperations!==void 0&&i.push(o[e].$allOperations)),e!=="$none"&&o.$allModels!==void 0&&(o.$allModels[r]!==void 0&&i.push(o.$allModels[r]),o.$allModels.$allOperations!==void 0&&i.push(o.$allModels.$allOperations)),o[r]!==void 0&&i.push(o[r]),o.$allOperations!==void 0&&i.push(o.$allOperations),n.concat(i))})}getAllBatchQueryCallbacks(){return this.batchCallbacks.get()}},ze=class t{constructor(e){this.head=e}static empty(){return new t}static single(e){return new t(new er(e))}isEmpty(){return this.head===void 0}append(e){return new t(new er(e,this.head))}getAllComputedFields(e){return this.head?.getAllComputedFields(e)}getAllClientExtensions(){return this.head?.getAllClientExtensions()}getAllModelExtensions(e){return this.head?.getAllModelExtensions(e)}getAllQueryCallbacks(e,r){return this.head?.getAllQueryCallbacks(e,r)??[]}getAllBatchQueryCallbacks(){return this.head?.getAllBatchQueryCallbacks()??[]}};u();c();m();p();d();l();var tr=class{constructor(e){this.name=e}};function Ri(t){return t instanceof tr}function Ai(t){return new tr(t)}u();c();m();p();d();l();u();c();m();p();d();l();var Si=Symbol(),xt=class{constructor(e){if(e!==Si)throw new Error("Skip instance can not be constructed directly")}ifUndefined(e){return e===void 0?rr:e}},rr=new xt(Si);function fe(t){return t instanceof xt}var Fa={findUnique:"findUnique",findUniqueOrThrow:"findUniqueOrThrow",findFirst:"findFirst",findFirstOrThrow:"findFirstOrThrow",findMany:"findMany",count:"aggregate",create:"createOne",createMany:"createMany",createManyAndReturn:"createManyAndReturn",update:"updateOne",updateMany:"updateMany",updateManyAndReturn:"updateManyAndReturn",upsert:"upsertOne",delete:"deleteOne",deleteMany:"deleteMany",executeRaw:"executeRaw",queryRaw:"queryRaw",aggregate:"aggregate",groupBy:"groupBy",runCommandRaw:"runCommandRaw",findRaw:"findRaw",aggregateRaw:"aggregateRaw"},Oi="explicitly `undefined` values are not allowed";function nr({modelName:t,action:e,args:r,runtimeDataModel:n,extensions:i=ze.empty(),callsite:o,clientMethod:s,errorFormat:a,clientVersion:f,previewFeatures:h,globalOmit:C}){let R=new Jr({runtimeDataModel:n,modelName:t,action:e,rootArgs:r,callsite:o,extensions:i,selectionPath:[],argumentPath:[],originalMethod:s,errorFormat:a,clientVersion:f,previewFeatures:h,globalOmit:C});return{modelName:t,action:Fa[e],query:Et(r,R)}}function Et({select:t,include:e,...r}={},n){let i=r.omit;return delete r.omit,{arguments:Di(r,n),selection:Ua(t,e,i,n)}}function Ua(t,e,r,n){return t?(e?n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"include",secondField:"select",selectionPath:n.getSelectionPath()}):r&&n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"omit",secondField:"select",selectionPath:n.getSelectionPath()}),Va(t,n)):Na(n,e,r)}function Na(t,e,r){let n={};return t.modelOrType&&!t.isRawAction()&&(n.$composites=!0,n.$scalars=!0),e&&qa(n,e,t),Ba(n,r,t),n}function qa(t,e,r){for(let[n,i]of Object.entries(e)){if(fe(i))continue;let o=r.nestSelection(n);if(Gr(i,o),i===!1||i===void 0){t[n]=!1;continue}let s=r.findField(n);if(s&&s.kind!=="object"&&r.throwValidationError({kind:"IncludeOnScalar",selectionPath:r.getSelectionPath().concat(n),outputType:r.getOutputTypeDescription()}),s){t[n]=Et(i===!0?{}:i,o);continue}if(i===!0){t[n]=!0;continue}t[n]=Et(i,o)}}function Ba(t,e,r){let n=r.getComputedFields(),i={...r.getGlobalOmit(),...e},o=Ci(i,n);for(let[s,a]of Object.entries(o)){if(fe(a))continue;Gr(a,r.nestSelection(s));let f=r.findField(s);n?.[s]&&!f||(t[s]=!a)}}function Va(t,e){let r={},n=e.getComputedFields(),i=Ti(t,n);for(let[o,s]of Object.entries(i)){if(fe(s))continue;let a=e.nestSelection(o);Gr(s,a);let f=e.findField(o);if(!(n?.[o]&&!f)){if(s===!1||s===void 0||fe(s)){r[o]=!1;continue}if(s===!0){f?.kind==="object"?r[o]=Et({},a):r[o]=!0;continue}r[o]=Et(s,a)}}return r}function ki(t,e){if(t===null)return null;if(typeof t=="string"||typeof t=="number"||typeof t=="boolean")return t;if(typeof t=="bigint")return{$type:"BigInt",value:String(t)};if(Ve(t)){if(jt(t))return{$type:"DateTime",value:t.toISOString()};e.throwValidationError({kind:"InvalidArgumentValue",selectionPath:e.getSelectionPath(),argumentPath:e.getArgumentPath(),argument:{name:e.getArgumentName(),typeNames:["Date"]},underlyingError:"Provided Date object is invalid"})}if(Ri(t))return{$type:"Param",value:t.name};if(Ke(t))return{$type:"FieldRef",value:{_ref:t.name,_container:t.modelName}};if(Array.isArray(t))return ja(t,e);if(ArrayBuffer.isView(t)){let{buffer:r,byteOffset:n,byteLength:i}=t;return{$type:"Bytes",value:b.from(r,n,i).toString("base64")}}if($a(t))return t.values;if(je(t))return{$type:"Decimal",value:t.toFixed()};if(t instanceof Ee){if(t!==zt.instances[t._getName()])throw new Error("Invalid ObjectEnumValue");return{$type:"Enum",value:t._getName()}}if(Qa(t))return t.toJSON();if(typeof t=="object")return Di(t,e);e.throwValidationError({kind:"InvalidArgumentValue",selectionPath:e.getSelectionPath(),argumentPath:e.getArgumentPath(),argument:{name:e.getArgumentName(),typeNames:[]},underlyingError:`We could not serialize ${Object.prototype.toString.call(t)} value. Serialize the object to JSON or implement a ".toJSON()" method on it`})}function Di(t,e){if(t.$type)return{$type:"Raw",value:t};let r={};for(let n in t){let i=t[n],o=e.nestArgument(n);fe(i)||(i!==void 0?r[n]=ki(i,o):e.isPreviewFeatureOn("strictUndefinedChecks")&&e.throwValidationError({kind:"InvalidArgumentValue",argumentPath:o.getArgumentPath(),selectionPath:e.getSelectionPath(),argument:{name:e.getArgumentName(),typeNames:[]},underlyingError:Oi}))}return r}function ja(t,e){let r=[];for(let n=0;n<t.length;n++){let i=e.nestArgument(String(n)),o=t[n];if(o===void 0||fe(o)){let s=o===void 0?"undefined":"Prisma.skip";e.throwValidationError({kind:"InvalidArgumentValue",selectionPath:i.getSelectionPath(),argumentPath:i.getArgumentPath(),argument:{name:`${e.getArgumentName()}[${n}]`,typeNames:[]},underlyingError:`Can not use \`${s}\` value within array. Use \`null\` or filter out \`${s}\` values`})}r.push(ki(o,i))}return r}function $a(t){return typeof t=="object"&&t!==null&&t.__prismaRawParameters__===!0}function Qa(t){return typeof t=="object"&&t!==null&&typeof t.toJSON=="function"}function Gr(t,e){t===void 0&&e.isPreviewFeatureOn("strictUndefinedChecks")&&e.throwValidationError({kind:"InvalidSelectionValue",selectionPath:e.getSelectionPath(),underlyingError:Oi})}var Jr=class t{constructor(e){this.params=e;this.params.modelName&&(this.modelOrType=this.params.runtimeDataModel.models[this.params.modelName]??this.params.runtimeDataModel.types[this.params.modelName])}modelOrType;throwValidationError(e){Zt({errors:[e],originalMethod:this.params.originalMethod,args:this.params.rootArgs??{},callsite:this.params.callsite,errorFormat:this.params.errorFormat,clientVersion:this.params.clientVersion,globalOmit:this.params.globalOmit})}getSelectionPath(){return this.params.selectionPath}getArgumentPath(){return this.params.argumentPath}getArgumentName(){return this.params.argumentPath[this.params.argumentPath.length-1]}getOutputTypeDescription(){if(!(!this.params.modelName||!this.modelOrType))return{name:this.params.modelName,fields:this.modelOrType.fields.map(e=>({name:e.name,typeName:"boolean",isRelation:e.kind==="object"}))}}isRawAction(){return["executeRaw","queryRaw","runCommandRaw","findRaw","aggregateRaw"].includes(this.params.action)}isPreviewFeatureOn(e){return this.params.previewFeatures.includes(e)}getComputedFields(){if(this.params.modelName)return this.params.extensions.getAllComputedFields(this.params.modelName)}findField(e){return this.modelOrType?.fields.find(r=>r.name===e)}nestSelection(e){let r=this.findField(e),n=r?.kind==="object"?r.type:void 0;return new t({...this.params,modelName:n,selectionPath:this.params.selectionPath.concat(e)})}getGlobalOmit(){return this.params.modelName&&this.shouldApplyGlobalOmit()?this.params.globalOmit?.[ve(this.params.modelName)]??{}:{}}shouldApplyGlobalOmit(){switch(this.params.action){case"findFirst":case"findFirstOrThrow":case"findUniqueOrThrow":case"findMany":case"upsert":case"findUnique":case"createManyAndReturn":case"create":case"update":case"updateManyAndReturn":case"delete":return!0;case"executeRaw":case"aggregateRaw":case"runCommandRaw":case"findRaw":case"createMany":case"deleteMany":case"groupBy":case"updateMany":case"count":case"aggregate":case"queryRaw":return!1;default:Me(this.params.action,"Unknown action")}}nestArgument(e){return new t({...this.params,argumentPath:this.params.argumentPath.concat(e)})}};u();c();m();p();d();l();function Ii(t){if(!t._hasPreviewFlag("metrics"))throw new K("`metrics` preview feature must be enabled in order to access metrics API",{clientVersion:t._clientVersion})}var Ye=class{_client;constructor(e){this._client=e}prometheus(e){return Ii(this._client),this._client._engine.metrics({format:"prometheus",...e})}json(e){return Ii(this._client),this._client._engine.metrics({format:"json",...e})}};u();c();m();p();d();l();function Mi(t,e){let r=ct(()=>Ja(e));Object.defineProperty(t,"dmmf",{get:()=>r.get()})}function Ja(t){throw new Error("Prisma.dmmf is not available when running in edge runtimes.")}function Wr(t){return Object.entries(t).map(([e,r])=>({name:e,...r}))}u();c();m();p();d();l();var Kr=new WeakMap,ir="$$PrismaTypedSql",Pt=class{constructor(e,r){Kr.set(this,{sql:e,values:r}),Object.defineProperty(this,ir,{value:ir})}get sql(){return Kr.get(this).sql}get values(){return Kr.get(this).values}};function _i(t){return(...e)=>new Pt(t,e)}function or(t){return t!=null&&t[ir]===ir}u();c();m();p();d();l();var Wo=it(Li());u();c();m();p();d();l();Fi();qn();$n();u();c();m();p();d();l();var ee=class t{constructor(e,r){if(e.length-1!==r.length)throw e.length===0?new TypeError("Expected at least 1 string"):new TypeError(`Expected ${e.length} strings to have ${e.length-1} values`);let n=r.reduce((s,a)=>s+(a instanceof t?a.values.length:1),0);this.values=new Array(n),this.strings=new Array(n+1),this.strings[0]=e[0];let i=0,o=0;for(;i<r.length;){let s=r[i++],a=e[i];if(s instanceof t){this.strings[o]+=s.strings[0];let f=0;for(;f<s.values.length;)this.values[o++]=s.values[f++],this.strings[o]=s.strings[f];this.strings[o]+=a}else this.values[o++]=s,this.strings[o]=a}}get sql(){let e=this.strings.length,r=1,n=this.strings[0];for(;r<e;)n+=`?${this.strings[r++]}`;return n}get statement(){let e=this.strings.length,r=1,n=this.strings[0];for(;r<e;)n+=`:${r}${this.strings[r++]}`;return n}get text(){let e=this.strings.length,r=1,n=this.strings[0];for(;r<e;)n+=`$${r}${this.strings[r++]}`;return n}inspect(){return{sql:this.sql,statement:this.statement,text:this.text,values:this.values}}};function Ui(t,e=",",r="",n=""){if(t.length===0)throw new TypeError("Expected `join([])` to be called with an array of multiple elements, but got an empty array");return new ee([r,...Array(t.length-1).fill(e),n],t)}function Hr(t){return new ee([t],[])}var Ni=Hr("");function zr(t,...e){return new ee(t,e)}u();c();m();p();d();l();u();c();m();p();d();l();function vt(t){return{getKeys(){return Object.keys(t)},getPropertyValue(e){return t[e]}}}u();c();m();p();d();l();function H(t,e){return{getKeys(){return[t]},getPropertyValue(){return e()}}}u();c();m();p();d();l();function _e(t){let e=new me;return{getKeys(){return t.getKeys()},getPropertyValue(r){return e.getOrCreate(r,()=>t.getPropertyValue(r))},getPropertyDescriptor(r){return t.getPropertyDescriptor?.(r)}}}u();c();m();p();d();l();u();c();m();p();d();l();var ar={enumerable:!0,configurable:!0,writable:!0};function lr(t){let e=new Set(t);return{getPrototypeOf:()=>Object.prototype,getOwnPropertyDescriptor:()=>ar,has:(r,n)=>e.has(n),set:(r,n,i)=>e.add(n)&&Reflect.set(r,n,i),ownKeys:()=>[...e]}}var qi=Symbol.for("nodejs.util.inspect.custom");function ae(t,e){let r=Wa(e),n=new Set,i=new Proxy(t,{get(o,s){if(n.has(s))return o[s];let a=r.get(s);return a?a.getPropertyValue(s):o[s]},has(o,s){if(n.has(s))return!0;let a=r.get(s);return a?a.has?.(s)??!0:Reflect.has(o,s)},ownKeys(o){let s=Bi(Reflect.ownKeys(o),r),a=Bi(Array.from(r.keys()),r);return[...new Set([...s,...a,...n])]},set(o,s,a){return r.get(s)?.getPropertyDescriptor?.(s)?.writable===!1?!1:(n.add(s),Reflect.set(o,s,a))},getOwnPropertyDescriptor(o,s){let a=Reflect.getOwnPropertyDescriptor(o,s);if(a&&!a.configurable)return a;let f=r.get(s);return f?f.getPropertyDescriptor?{...ar,...f?.getPropertyDescriptor(s)}:ar:a},defineProperty(o,s,a){return n.add(s),Reflect.defineProperty(o,s,a)},getPrototypeOf:()=>Object.prototype});return i[qi]=function(){let o={...this};return delete o[qi],o},i}function Wa(t){let e=new Map;for(let r of t){let n=r.getKeys();for(let i of n)e.set(i,r)}return e}function Bi(t,e){return t.filter(r=>e.get(r)?.has?.(r)??!0)}u();c();m();p();d();l();function Xe(t){return{getKeys(){return t},has(){return!1},getPropertyValue(){}}}u();c();m();p();d();l();function ur(t,e){return{batch:t,transaction:e?.kind==="batch"?{isolationLevel:e.options.isolationLevel}:void 0}}u();c();m();p();d();l();function Vi(t){if(t===void 0)return"";let e=He(t);return new $e(0,{colors:Wt}).write(e).toString()}u();c();m();p();d();l();var Ka="P2037";function cr({error:t,user_facing_error:e},r,n){return e.error_code?new Z(Ha(e,n),{code:e.error_code,clientVersion:r,meta:e.meta,batchRequestIdx:e.batch_request_idx}):new Q(t,{clientVersion:r,batchRequestIdx:e.batch_request_idx})}function Ha(t,e){let r=t.message;return(e==="postgresql"||e==="postgres"||e==="mysql")&&t.error_code===Ka&&(r+=`
Prisma Accelerate has built-in connection pooling to prevent such errors: https://pris.ly/client/error-accelerate`),r}u();c();m();p();d();l();u();c();m();p();d();l();u();c();m();p();d();l();u();c();m();p();d();l();u();c();m();p();d();l();var Yr=class{getLocation(){return null}};function Ce(t){return typeof $EnabledCallSite=="function"&&t!=="minimal"?new $EnabledCallSite:new Yr}u();c();m();p();d();l();u();c();m();p();d();l();u();c();m();p();d();l();var ji={_avg:!0,_count:!0,_sum:!0,_min:!0,_max:!0};function Ze(t={}){let e=Ya(t);return Object.entries(e).reduce((n,[i,o])=>(ji[i]!==void 0?n.select[i]={select:o}:n[i]=o,n),{select:{}})}function Ya(t={}){return typeof t._count=="boolean"?{...t,_count:{_all:t._count}}:t}function mr(t={}){return e=>(typeof t._count=="boolean"&&(e._count=e._count._all),e)}function $i(t,e){let r=mr(t);return e({action:"aggregate",unpacker:r,argsMapper:Ze})(t)}u();c();m();p();d();l();function Xa(t={}){let{select:e,...r}=t;return typeof e=="object"?Ze({...r,_count:e}):Ze({...r,_count:{_all:!0}})}function Za(t={}){return typeof t.select=="object"?e=>mr(t)(e)._count:e=>mr(t)(e)._count._all}function Qi(t,e){return e({action:"count",unpacker:Za(t),argsMapper:Xa})(t)}u();c();m();p();d();l();function el(t={}){let e=Ze(t);if(Array.isArray(e.by))for(let r of e.by)typeof r=="string"&&(e.select[r]=!0);else typeof e.by=="string"&&(e.select[e.by]=!0);return e}function tl(t={}){return e=>(typeof t?._count=="boolean"&&e.forEach(r=>{r._count=r._count._all}),e)}function Ji(t,e){return e({action:"groupBy",unpacker:tl(t),argsMapper:el})(t)}function Gi(t,e,r){if(e==="aggregate")return n=>$i(n,r);if(e==="count")return n=>Qi(n,r);if(e==="groupBy")return n=>Ji(n,r)}u();c();m();p();d();l();function Wi(t,e){let r=e.fields.filter(i=>!i.relationName),n=ni(r,"name");return new Proxy({},{get(i,o){if(o in i||typeof o=="symbol")return i[o];let s=n[o];if(s)return new gt(t,o,s.type,s.isList,s.kind==="enum")},...lr(Object.keys(n))})}u();c();m();p();d();l();u();c();m();p();d();l();var Ki=t=>Array.isArray(t)?t:t.split("."),Xr=(t,e)=>Ki(e).reduce((r,n)=>r&&r[n],t),Hi=(t,e,r)=>Ki(e).reduceRight((n,i,o,s)=>Object.assign({},Xr(t,s.slice(0,o)),{[i]:n}),r);function rl(t,e){return t===void 0||e===void 0?[]:[...e,"select",t]}function nl(t,e,r){return e===void 0?t??{}:Hi(e,r,t||!0)}function Zr(t,e,r,n,i,o){let a=t._runtimeDataModel.models[e].fields.reduce((f,h)=>({...f,[h.name]:h}),{});return f=>{let h=Ce(t._errorFormat),C=rl(n,i),R=nl(f,o,C),k=r({dataPath:C,callsite:h})(R),A=il(t,e);return new Proxy(k,{get(_,O){if(!A.includes(O))return _[O];let ye=[a[O].type,r,O],z=[C,R];return Zr(t,...ye,...z)},...lr([...A,...Object.getOwnPropertyNames(k)])})}}function il(t,e){return t._runtimeDataModel.models[e].fields.filter(r=>r.kind==="object").map(r=>r.name)}var ol=["findUnique","findUniqueOrThrow","findFirst","findFirstOrThrow","create","update","upsert","delete"],sl=["aggregate","count","groupBy"];function en(t,e){let r=t._extensions.getAllModelExtensions(e)??{},n=[al(t,e),ul(t,e),vt(r),H("name",()=>e),H("$name",()=>e),H("$parent",()=>t._appliedParent)];return ae({},n)}function al(t,e){let r=de(e),n=Object.keys(mt).concat("count");return{getKeys(){return n},getPropertyValue(i){let o=i,s=a=>f=>{let h=Ce(t._errorFormat);return t._createPrismaPromise(C=>{let R={args:f,dataPath:[],action:o,model:e,clientMethod:`${r}.${i}`,jsModelName:r,transaction:C,callsite:h};return t._request({...R,...a})},{action:o,args:f,model:e})};return ol.includes(o)?Zr(t,e,s):ll(i)?Gi(t,i,s):s({})}}}function ll(t){return sl.includes(t)}function ul(t,e){return _e(H("fields",()=>{let r=t._runtimeDataModel.models[e];return Wi(e,r)}))}u();c();m();p();d();l();function zi(t){return t.replace(/^./,e=>e.toUpperCase())}var tn=Symbol();function Tt(t){let e=[cl(t),ml(t),H(tn,()=>t),H("$parent",()=>t._appliedParent)],r=t._extensions.getAllClientExtensions();return r&&e.push(vt(r)),ae(t,e)}function cl(t){let e=Object.getPrototypeOf(t._originalClient),r=[...new Set(Object.getOwnPropertyNames(e))];return{getKeys(){return r},getPropertyValue(n){return t[n]}}}function ml(t){let e=Object.keys(t._runtimeDataModel.models),r=e.map(de),n=[...new Set(e.concat(r))];return _e({getKeys(){return n},getPropertyValue(i){let o=zi(i);if(t._runtimeDataModel.models[o]!==void 0)return en(t,o);if(t._runtimeDataModel.models[i]!==void 0)return en(t,i)},getPropertyDescriptor(i){if(!r.includes(i))return{enumerable:!1}}})}function Yi(t){return t[tn]?t[tn]:t}function Xi(t){if(typeof t=="function")return t(this);if(t.client?.__AccelerateEngine){let r=t.client.__AccelerateEngine;this._originalClient._engine=new r(this._originalClient._accelerateEngineConfig)}let e=Object.create(this._originalClient,{_extensions:{value:this._extensions.append(t)},_appliedParent:{value:this,configurable:!0},$on:{value:void 0}});return Tt(e)}u();c();m();p();d();l();u();c();m();p();d();l();function Zi({result:t,modelName:e,select:r,omit:n,extensions:i}){let o=i.getAllComputedFields(e);if(!o)return t;let s=[],a=[];for(let f of Object.values(o)){if(n){if(n[f.name])continue;let h=f.needs.filter(C=>n[C]);h.length>0&&a.push(Xe(h))}else if(r){if(!r[f.name])continue;let h=f.needs.filter(C=>!r[C]);h.length>0&&a.push(Xe(h))}pl(t,f.needs)&&s.push(dl(f,ae(t,s)))}return s.length>0||a.length>0?ae(t,[...s,...a]):t}function pl(t,e){return e.every(r=>Ur(t,r))}function dl(t,e){return _e(H(t.name,()=>t.compute(e)))}u();c();m();p();d();l();function pr({visitor:t,result:e,args:r,runtimeDataModel:n,modelName:i}){if(Array.isArray(e)){for(let s=0;s<e.length;s++)e[s]=pr({result:e[s],args:r,modelName:i,runtimeDataModel:n,visitor:t});return e}let o=t(e,i,r)??e;return r.include&&eo({includeOrSelect:r.include,result:o,parentModelName:i,runtimeDataModel:n,visitor:t}),r.select&&eo({includeOrSelect:r.select,result:o,parentModelName:i,runtimeDataModel:n,visitor:t}),o}function eo({includeOrSelect:t,result:e,parentModelName:r,runtimeDataModel:n,visitor:i}){for(let[o,s]of Object.entries(t)){if(!s||e[o]==null||fe(s))continue;let f=n.models[r].fields.find(C=>C.name===o);if(!f||f.kind!=="object"||!f.relationName)continue;let h=typeof s=="object"?s:{};e[o]=pr({visitor:i,result:e[o],args:h,modelName:f.type,runtimeDataModel:n})}}function to({result:t,modelName:e,args:r,extensions:n,runtimeDataModel:i,globalOmit:o}){return n.isEmpty()||t==null||typeof t!="object"||!i.models[e]?t:pr({result:t,args:r??{},modelName:e,runtimeDataModel:i,visitor:(a,f,h)=>{let C=de(f);return Zi({result:a,modelName:C,select:h.select,omit:h.select?void 0:{...o?.[C],...h.omit},extensions:n})}})}u();c();m();p();d();l();u();c();m();p();d();l();l();u();c();m();p();d();l();var fl=["$connect","$disconnect","$on","$transaction","$extends"],ro=fl;function no(t){if(t instanceof ee)return gl(t);if(or(t))return yl(t);if(Array.isArray(t)){let r=[t[0]];for(let n=1;n<t.length;n++)r[n]=Ct(t[n]);return r}let e={};for(let r in t)e[r]=Ct(t[r]);return e}function gl(t){return new ee(t.strings,t.values)}function yl(t){return new Pt(t.sql,t.values)}function Ct(t){if(typeof t!="object"||t==null||t instanceof Ee||Ke(t))return t;if(je(t))return new be(t.toFixed());if(Ve(t))return new Date(+t);if(ArrayBuffer.isView(t))return t.slice(0);if(Array.isArray(t)){let e=t.length,r;for(r=Array(e);e--;)r[e]=Ct(t[e]);return r}if(typeof t=="object"){let e={};for(let r in t)r==="__proto__"?Object.defineProperty(e,r,{value:Ct(t[r]),configurable:!0,enumerable:!0,writable:!0}):e[r]=Ct(t[r]);return e}Me(t,"Unknown value")}function oo(t,e,r,n=0){return t._createPrismaPromise(i=>{let o=e.customDataProxyFetch;return"transaction"in e&&i!==void 0&&(e.transaction?.kind==="batch"&&e.transaction.lock.then(),e.transaction=i),n===r.length?t._executeRequest(e):r[n]({model:e.model,operation:e.model?e.action:e.clientMethod,args:no(e.args??{}),__internalParams:e,query:(s,a=e)=>{let f=a.customDataProxyFetch;return a.customDataProxyFetch=uo(o,f),a.args=s,oo(t,a,r,n+1)}})})}function so(t,e){let{jsModelName:r,action:n,clientMethod:i}=e,o=r?n:i;if(t._extensions.isEmpty())return t._executeRequest(e);let s=t._extensions.getAllQueryCallbacks(r??"$none",o);return oo(t,e,s)}function ao(t){return e=>{let r={requests:e},n=e[0].extensions.getAllBatchQueryCallbacks();return n.length?lo(r,n,0,t):t(r)}}function lo(t,e,r,n){if(r===e.length)return n(t);let i=t.customDataProxyFetch,o=t.requests[0].transaction;return e[r]({args:{queries:t.requests.map(s=>({model:s.modelName,operation:s.action,args:s.args})),transaction:o?{isolationLevel:o.kind==="batch"?o.isolationLevel:void 0}:void 0},__internalParams:t,query(s,a=t){let f=a.customDataProxyFetch;return a.customDataProxyFetch=uo(i,f),lo(a,e,r+1,n)}})}var io=t=>t;function uo(t=io,e=io){return r=>t(e(r))}u();c();m();p();d();l();var co=G("prisma:client"),mo={Vercel:"vercel","Netlify CI":"netlify"};function po({postinstall:t,ciName:e,clientVersion:r}){if(co("checkPlatformCaching:postinstall",t),co("checkPlatformCaching:ciName",e),t===!0&&e&&e in mo){let n=`Prisma has detected that this project was built on ${e}, which caches dependencies. This leads to an outdated Prisma Client because Prisma's auto-generation isn't triggered. To fix this, make sure to run the \`prisma generate\` command during the build process.

Learn how: https://pris.ly/d/${mo[e]}-build`;throw console.error(n),new I(n,r)}}u();c();m();p();d();l();function fo(t,e){return t?t.datasources?t.datasources:t.datasourceUrl?{[e[0]]:{url:t.datasourceUrl}}:{}:{}}u();c();m();p();d();l();u();c();m();p();d();l();var hl=()=>globalThis.process?.release?.name==="node",bl=()=>!!globalThis.Bun||!!globalThis.process?.versions?.bun,wl=()=>!!globalThis.Deno,xl=()=>typeof globalThis.Netlify=="object",El=()=>typeof globalThis.EdgeRuntime=="object",Pl=()=>globalThis.navigator?.userAgent==="Cloudflare-Workers";function vl(){return[[xl,"netlify"],[El,"edge-light"],[Pl,"workerd"],[wl,"deno"],[bl,"bun"],[hl,"node"]].flatMap(r=>r[0]()?[r[1]]:[]).at(0)??""}var Tl={node:"Node.js",workerd:"Cloudflare Workers",deno:"Deno and Deno Deploy",netlify:"Netlify Edge Functions","edge-light":"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)"};function Re(){let t=vl();return{id:t,prettyName:Tl[t]||t,isEdge:["workerd","deno","netlify","edge-light"].includes(t)}}u();c();m();p();d();l();u();c();m();p();d();l();u();c();m();p();d();l();l();u();c();m();p();d();l();l();function go(t,e){throw new Error(e)}function Cl(t){return t!==null&&typeof t=="object"&&typeof t.$type=="string"}function Rl(t,e){let r={};for(let n of Object.keys(t))r[n]=e(t[n],n);return r}function et(t){return t===null?t:Array.isArray(t)?t.map(et):typeof t=="object"?Cl(t)?Al(t):t.constructor!==null&&t.constructor.name!=="Object"?t:Rl(t,et):t}function Al({$type:t,value:e}){switch(t){case"BigInt":return BigInt(e);case"Bytes":{let{buffer:r,byteOffset:n,byteLength:i}=b.from(e,"base64");return new Uint8Array(r,n,i)}case"DateTime":return new Date(e);case"Decimal":return new v(e);case"Json":return JSON.parse(e);default:go(e,"Unknown tagged value")}}var yo="6.14.0";u();c();m();p();d();l();function dr({inlineDatasources:t,overrideDatasources:e,env:r,clientVersion:n}){let i,o=Object.keys(t)[0],s=t[o]?.url,a=e[o]?.url;if(o===void 0?i=void 0:a?i=a:s?.value?i=s.value:s?.fromEnvVar&&(i=r[s.fromEnvVar]),s?.fromEnvVar!==void 0&&i===void 0)throw Re().id==="workerd"?new I(`error: Environment variable not found: ${s.fromEnvVar}.

In Cloudflare module Workers, environment variables are available only in the Worker's \`env\` parameter of \`fetch\`.
To solve this, provide the connection string directly: https://pris.ly/d/cloudflare-datasource-url`,n):new I(`error: Environment variable not found: ${s.fromEnvVar}.`,n);if(i===void 0)throw new I("error: Missing URL environment variable, value, or override.",n);return i}u();c();m();p();d();l();u();c();m();p();d();l();function ho(t){if(t?.kind==="itx")return t.options.id}u();c();m();p();d();l();var rn,bo={async loadLibrary(t){let{clientVersion:e,adapter:r,engineWasm:n}=t;if(r===void 0)throw new I(`The \`adapter\` option for \`PrismaClient\` is required in this context (${Re().prettyName})`,e);if(n===void 0)throw new I("WASM engine was unexpectedly `undefined`",e);rn===void 0&&(rn=(async()=>{let o=await n.getRuntime(),s=await n.getQueryEngineWasmModule();if(s==null)throw new I("The loaded wasm module was unexpectedly `undefined` or `null` once loaded",e);let a={"./query_engine_bg.js":o},f=new WebAssembly.Instance(s,a),h=f.exports.__wbindgen_start;return o.__wbg_set_wasm(f.exports),h(),o.QueryEngine})());let i=await rn;return{debugPanic(){return Promise.reject("{}")},dmmf(){return Promise.resolve("{}")},version(){return{commit:"unknown",version:"unknown"}},QueryEngine:i}}};var Ol="P2036",ge=G("prisma:client:libraryEngine");function kl(t){return t.item_type==="query"&&"query"in t}function Dl(t){return"level"in t?t.level==="error"&&t.message==="PANIC":!1}var sO=[...Dr,"native"],Il=0xffffffffffffffffn,nn=1n;function Ml(){let t=nn++;return nn>Il&&(nn=1n),t}var Rt=class{name="LibraryEngine";engine;libraryInstantiationPromise;libraryStartingPromise;libraryStoppingPromise;libraryStarted;executingQueryPromise;config;QueryEngineConstructor;libraryLoader;library;logEmitter;libQueryEnginePath;binaryTarget;datasourceOverrides;datamodel;logQueries;logLevel;lastQuery;loggerRustPanic;tracingHelper;adapterPromise;versionInfo;constructor(e,r){this.libraryLoader=r??bo,this.config=e,this.libraryStarted=!1,this.logQueries=e.logQueries??!1,this.logLevel=e.logLevel??"error",this.logEmitter=e.logEmitter,this.datamodel=e.inlineSchema,this.tracingHelper=e.tracingHelper,e.enableDebugLogs&&(this.logLevel="debug");let n=Object.keys(e.overrideDatasources)[0],i=e.overrideDatasources[n]?.url;n!==void 0&&i!==void 0&&(this.datasourceOverrides={[n]:i}),this.libraryInstantiationPromise=this.instantiateLibrary()}wrapEngine(e){return{applyPendingMigrations:e.applyPendingMigrations?.bind(e),commitTransaction:this.withRequestId(e.commitTransaction.bind(e)),connect:this.withRequestId(e.connect.bind(e)),disconnect:this.withRequestId(e.disconnect.bind(e)),metrics:e.metrics?.bind(e),query:this.withRequestId(e.query.bind(e)),rollbackTransaction:this.withRequestId(e.rollbackTransaction.bind(e)),sdlSchema:e.sdlSchema?.bind(e),startTransaction:this.withRequestId(e.startTransaction.bind(e)),trace:e.trace.bind(e),free:e.free?.bind(e)}}withRequestId(e){return async(...r)=>{let n=Ml().toString();try{return await e(...r,n)}finally{if(this.tracingHelper.isEnabled()){let i=await this.engine?.trace(n);if(i){let o=JSON.parse(i);this.tracingHelper.dispatchEngineSpans(o.spans)}}}}}async applyPendingMigrations(){throw new Error("Cannot call this method from this type of engine instance")}async transaction(e,r,n){await this.start();let i=await this.adapterPromise,o=JSON.stringify(r),s;if(e==="start"){let f=JSON.stringify({max_wait:n.maxWait,timeout:n.timeout,isolation_level:n.isolationLevel});s=await this.engine?.startTransaction(f,o)}else e==="commit"?s=await this.engine?.commitTransaction(n.id,o):e==="rollback"&&(s=await this.engine?.rollbackTransaction(n.id,o));let a=this.parseEngineResponse(s);if(_l(a)){let f=this.getExternalAdapterError(a,i?.errorRegistry);throw f?f.error:new Z(a.message,{code:a.error_code,clientVersion:this.config.clientVersion,meta:a.meta})}else if(typeof a.message=="string")throw new Q(a.message,{clientVersion:this.config.clientVersion});return a}async instantiateLibrary(){if(ge("internalSetup"),this.libraryInstantiationPromise)return this.libraryInstantiationPromise;this.binaryTarget=await this.getCurrentBinaryTarget(),await this.tracingHelper.runInChildSpan("load_engine",()=>this.loadEngine()),this.version()}async getCurrentBinaryTarget(){}parseEngineResponse(e){if(!e)throw new Q("Response from the Engine was empty",{clientVersion:this.config.clientVersion});try{return JSON.parse(e)}catch{throw new Q("Unable to JSON.parse response from engine",{clientVersion:this.config.clientVersion})}}async loadEngine(){if(!this.engine){this.QueryEngineConstructor||(this.library=await this.libraryLoader.loadLibrary(this.config),this.QueryEngineConstructor=this.library.QueryEngine);try{let e=new w(this);this.adapterPromise||(this.adapterPromise=this.config.adapter?.connect()?.then(qt));let r=await this.adapterPromise;r&&ge("Using driver adapter: %O",r),this.engine=this.wrapEngine(new this.QueryEngineConstructor({datamodel:this.datamodel,env:g.env,logQueries:this.config.logQueries??!1,ignoreEnvVarErrors:!0,datasourceOverrides:this.datasourceOverrides??{},logLevel:this.logLevel,configDir:this.config.cwd,engineProtocol:"json",enableTracing:this.tracingHelper.isEnabled()},n=>{e.deref()?.logger(n)},r))}catch(e){let r=e,n=this.parseInitError(r.message);throw typeof n=="string"?r:new I(n.message,this.config.clientVersion,n.error_code)}}}logger(e){let r=this.parseEngineResponse(e);r&&(r.level=r?.level.toLowerCase()??"unknown",kl(r)?this.logEmitter.emit("query",{timestamp:new Date,query:r.query,params:r.params,duration:Number(r.duration_ms),target:r.module_path}):(Dl(r),this.logEmitter.emit(r.level,{timestamp:new Date,message:r.message,target:r.module_path})))}parseInitError(e){try{return JSON.parse(e)}catch{}return e}parseRequestError(e){try{return JSON.parse(e)}catch{}return e}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the library engine since Prisma 5.0.0, it is only relevant and implemented for the binary engine. Please add your event listener to the `process` object directly instead.')}async start(){if(this.libraryInstantiationPromise||(this.libraryInstantiationPromise=this.instantiateLibrary()),await this.libraryInstantiationPromise,await this.libraryStoppingPromise,this.libraryStartingPromise)return ge(`library already starting, this.libraryStarted: ${this.libraryStarted}`),this.libraryStartingPromise;if(this.libraryStarted)return;let e=async()=>{ge("library starting");try{let r={traceparent:this.tracingHelper.getTraceParent()};await this.engine?.connect(JSON.stringify(r)),this.libraryStarted=!0,this.adapterPromise||(this.adapterPromise=this.config.adapter?.connect()?.then(qt)),await this.adapterPromise,ge("library started")}catch(r){let n=this.parseInitError(r.message);throw typeof n=="string"?r:new I(n.message,this.config.clientVersion,n.error_code)}finally{this.libraryStartingPromise=void 0}};return this.libraryStartingPromise=this.tracingHelper.runInChildSpan("connect",e),this.libraryStartingPromise}async stop(){if(await this.libraryInstantiationPromise,await this.libraryStartingPromise,await this.executingQueryPromise,this.libraryStoppingPromise)return ge("library is already stopping"),this.libraryStoppingPromise;if(!this.libraryStarted){await(await this.adapterPromise)?.dispose(),this.adapterPromise=void 0;return}let e=async()=>{await new Promise(n=>setImmediate(n)),ge("library stopping");let r={traceparent:this.tracingHelper.getTraceParent()};await this.engine?.disconnect(JSON.stringify(r)),this.engine?.free&&this.engine.free(),this.engine=void 0,this.libraryStarted=!1,this.libraryStoppingPromise=void 0,this.libraryInstantiationPromise=void 0,await(await this.adapterPromise)?.dispose(),this.adapterPromise=void 0,ge("library stopped")};return this.libraryStoppingPromise=this.tracingHelper.runInChildSpan("disconnect",e),this.libraryStoppingPromise}version(){return this.versionInfo=this.library?.version(),this.versionInfo?.version??"unknown"}debugPanic(e){return this.library?.debugPanic(e)}async request(e,{traceparent:r,interactiveTransaction:n}){ge(`sending request, this.libraryStarted: ${this.libraryStarted}`);let i=JSON.stringify({traceparent:r}),o=JSON.stringify(e);try{await this.start();let s=await this.adapterPromise;this.executingQueryPromise=this.engine?.query(o,i,n?.id),this.lastQuery=o;let a=this.parseEngineResponse(await this.executingQueryPromise);if(a.errors)throw a.errors.length===1?this.buildQueryError(a.errors[0],s?.errorRegistry):new Q(JSON.stringify(a.errors),{clientVersion:this.config.clientVersion});if(this.loggerRustPanic)throw this.loggerRustPanic;return{data:a}}catch(s){if(s instanceof I)throw s;s.code==="GenericFailure"&&s.message?.startsWith("PANIC:");let a=this.parseRequestError(s.message);throw typeof a=="string"?s:new Q(`${a.message}
${a.backtrace}`,{clientVersion:this.config.clientVersion})}}async requestBatch(e,{transaction:r,traceparent:n}){ge("requestBatch");let i=ur(e,r);await this.start();let o=await this.adapterPromise;this.lastQuery=JSON.stringify(i),this.executingQueryPromise=this.engine?.query(this.lastQuery,JSON.stringify({traceparent:n}),ho(r));let s=await this.executingQueryPromise,a=this.parseEngineResponse(s);if(a.errors)throw a.errors.length===1?this.buildQueryError(a.errors[0],o?.errorRegistry):new Q(JSON.stringify(a.errors),{clientVersion:this.config.clientVersion});let{batchResult:f,errors:h}=a;if(Array.isArray(f))return f.map(C=>C.errors&&C.errors.length>0?this.loggerRustPanic??this.buildQueryError(C.errors[0],o?.errorRegistry):{data:C});throw h&&h.length===1?new Error(h[0].error):new Error(JSON.stringify(a))}buildQueryError(e,r){e.user_facing_error.is_panic;let n=this.getExternalAdapterError(e.user_facing_error,r);return n?n.error:cr(e,this.config.clientVersion,this.config.activeProvider)}getExternalAdapterError(e,r){if(e.error_code===Ol&&r){let n=e.meta?.id;Bt(typeof n=="number","Malformed external JS error received from the engine");let i=r.consumeError(n);return Bt(i,"External error with reported id was not registered"),i}}async metrics(e){await this.start();let r=await this.engine.metrics(JSON.stringify(e));return e.format==="prometheus"?r:this.parseEngineResponse(r)}};function _l(t){return typeof t=="object"&&t!==null&&t.error_code!==void 0}u();c();m();p();d();l();var At="Accelerate has not been setup correctly. Make sure your client is using `.$extends(withAccelerate())`. See https://pris.ly/d/accelerate-getting-started",fr=class{constructor(e){this.config=e;this.resolveDatasourceUrl=this.config.accelerateUtils?.resolveDatasourceUrl,this.getBatchRequestPayload=this.config.accelerateUtils?.getBatchRequestPayload,this.prismaGraphQLToJSError=this.config.accelerateUtils?.prismaGraphQLToJSError,this.PrismaClientUnknownRequestError=this.config.accelerateUtils?.PrismaClientUnknownRequestError,this.PrismaClientInitializationError=this.config.accelerateUtils?.PrismaClientInitializationError,this.PrismaClientKnownRequestError=this.config.accelerateUtils?.PrismaClientKnownRequestError,this.debug=this.config.accelerateUtils?.debug,this.engineVersion=this.config.accelerateUtils?.engineVersion,this.clientVersion=this.config.accelerateUtils?.clientVersion}name="AccelerateEngine";resolveDatasourceUrl;getBatchRequestPayload;prismaGraphQLToJSError;PrismaClientUnknownRequestError;PrismaClientInitializationError;PrismaClientKnownRequestError;debug;engineVersion;clientVersion;onBeforeExit(e){}async start(){}async stop(){}version(e){return"unknown"}transaction(e,r,n){throw new I(At,this.config.clientVersion)}metrics(e){throw new I(At,this.config.clientVersion)}request(e,r){throw new I(At,this.config.clientVersion)}requestBatch(e,r){throw new I(At,this.config.clientVersion)}applyPendingMigrations(){throw new I(At,this.config.clientVersion)}};u();c();m();p();d();l();function wo({url:t,adapter:e,copyEngine:r,targetBuildType:n}){let i=[],o=[],s=O=>{i.push({_tag:"warning",value:O})},a=O=>{let D=O.join(`
`);o.push({_tag:"error",value:D})},f=!!t?.startsWith("prisma://"),h=Lr(t),C=!!e,R=f||h;!C&&r&&R&&s(["recommend--no-engine","In production, we recommend using `prisma generate --no-engine` (See: `prisma generate --help`)"]);let k=R||!r;C&&(k||n==="edge")&&(n==="edge"?a(["Prisma Client was configured to use the `adapter` option but it was imported via its `/edge` endpoint.","Please either remove the `/edge` endpoint or remove the `adapter` from the Prisma Client constructor."]):r?f&&a(["Prisma Client was configured to use the `adapter` option but the URL was a `prisma://` URL.","Please either use the `prisma://` URL or remove the `adapter` from the Prisma Client constructor."]):a(["Prisma Client was configured to use the `adapter` option but `prisma generate` was run with `--no-engine`.","Please run `prisma generate` without `--no-engine` to be able to use Prisma Client with the adapter."]));let A={accelerate:k,ppg:h,driverAdapters:C};function _(O){return O.length>0}return _(o)?{ok:!1,diagnostics:{warnings:i,errors:o},isUsing:A}:{ok:!0,diagnostics:{warnings:i},isUsing:A}}function xo({copyEngine:t=!0},e){let r;try{r=dr({inlineDatasources:e.inlineDatasources,overrideDatasources:e.overrideDatasources,env:{...e.env,...g.env},clientVersion:e.clientVersion})}catch{}let{ok:n,isUsing:i,diagnostics:o}=wo({url:r,adapter:e.adapter,copyEngine:t,targetBuildType:"wasm-engine-edge"});for(let R of o.warnings)ut(...R.value);if(!n){let R=o.errors[0];throw new K(R.value,{clientVersion:e.clientVersion})}let s=Be(e.generator),a=s==="library",f=s==="binary",h=s==="client",C=(i.accelerate||i.ppg)&&!i.driverAdapters;if(i.accelerate,i.driverAdapters)return new Rt(e);if(i.accelerate)return new fr(e);{let R=[`PrismaClient failed to initialize because it wasn't configured to run in this environment (${Re().prettyName}).`,"In order to run Prisma Client in an edge runtime, you will need to configure one of the following options:","- Enable Driver Adapters: https://pris.ly/d/driver-adapters","- Enable Accelerate: https://pris.ly/d/accelerate"];throw new K(R.join(`
`),{clientVersion:e.clientVersion})}return"wasm-engine-edge"}u();c();m();p();d();l();function gr({generator:t}){return t?.previewFeatures??[]}u();c();m();p();d();l();var Eo=t=>({command:t});u();c();m();p();d();l();u();c();m();p();d();l();var Po=t=>t.strings.reduce((e,r,n)=>`${e}@P${n}${r}`);u();c();m();p();d();l();l();function tt(t){try{return vo(t,"fast")}catch{return vo(t,"slow")}}function vo(t,e){return JSON.stringify(t.map(r=>Co(r,e)))}function Co(t,e){if(Array.isArray(t))return t.map(r=>Co(r,e));if(typeof t=="bigint")return{prisma__type:"bigint",prisma__value:t.toString()};if(Ve(t))return{prisma__type:"date",prisma__value:t.toJSON()};if(be.isDecimal(t))return{prisma__type:"decimal",prisma__value:t.toJSON()};if(b.isBuffer(t))return{prisma__type:"bytes",prisma__value:t.toString("base64")};if(Ll(t))return{prisma__type:"bytes",prisma__value:b.from(t).toString("base64")};if(ArrayBuffer.isView(t)){let{buffer:r,byteOffset:n,byteLength:i}=t;return{prisma__type:"bytes",prisma__value:b.from(r,n,i).toString("base64")}}return typeof t=="object"&&e==="slow"?Ro(t):t}function Ll(t){return t instanceof ArrayBuffer||t instanceof SharedArrayBuffer?!0:typeof t=="object"&&t!==null?t[Symbol.toStringTag]==="ArrayBuffer"||t[Symbol.toStringTag]==="SharedArrayBuffer":!1}function Ro(t){if(typeof t!="object"||t===null)return t;if(typeof t.toJSON=="function")return t.toJSON();if(Array.isArray(t))return t.map(To);let e={};for(let r of Object.keys(t))e[r]=To(t[r]);return e}function To(t){return typeof t=="bigint"?t.toString():Ro(t)}var Fl=/^(\s*alter\s)/i,Ao=G("prisma:client");function on(t,e,r,n){if(!(t!=="postgresql"&&t!=="cockroachdb")&&r.length>0&&Fl.exec(e))throw new Error(`Running ALTER using ${n} is not supported
Using the example below you can still execute your query with Prisma, but please note that it is vulnerable to SQL injection attacks and requires you to take care of input sanitization.

Example:
  await prisma.$executeRawUnsafe(\`ALTER USER prisma WITH PASSWORD '\${password}'\`)

More Information: https://pris.ly/d/execute-raw
`)}var sn=({clientMethod:t,activeProvider:e})=>r=>{let n="",i;if(or(r))n=r.sql,i={values:tt(r.values),__prismaRawParameters__:!0};else if(Array.isArray(r)){let[o,...s]=r;n=o,i={values:tt(s||[]),__prismaRawParameters__:!0}}else switch(e){case"sqlite":case"mysql":{n=r.sql,i={values:tt(r.values),__prismaRawParameters__:!0};break}case"cockroachdb":case"postgresql":case"postgres":{n=r.text,i={values:tt(r.values),__prismaRawParameters__:!0};break}case"sqlserver":{n=Po(r),i={values:tt(r.values),__prismaRawParameters__:!0};break}default:throw new Error(`The ${e} provider does not support ${t}`)}return i?.values?Ao(`prisma.${t}(${n}, ${i.values})`):Ao(`prisma.${t}(${n})`),{query:n,parameters:i}},So={requestArgsToMiddlewareArgs(t){return[t.strings,...t.values]},middlewareArgsToRequestArgs(t){let[e,...r]=t;return new ee(e,r)}},Oo={requestArgsToMiddlewareArgs(t){return[t]},middlewareArgsToRequestArgs(t){return t[0]}};u();c();m();p();d();l();function an(t){return function(r,n){let i,o=(s=t)=>{try{return s===void 0||s?.kind==="itx"?i??=ko(r(s)):ko(r(s))}catch(a){return Promise.reject(a)}};return{get spec(){return n},then(s,a){return o().then(s,a)},catch(s){return o().catch(s)},finally(s){return o().finally(s)},requestTransaction(s){let a=o(s);return a.requestTransaction?a.requestTransaction(s):a},[Symbol.toStringTag]:"PrismaPromise"}}}function ko(t){return typeof t.then=="function"?t:Promise.resolve(t)}u();c();m();p();d();l();var Ul=Ir.split(".")[0],Nl={isEnabled(){return!1},getTraceParent(){return"00-10-10-00"},dispatchEngineSpans(){},getActiveContext(){},runInChildSpan(t,e){return e()}},ln=class{isEnabled(){return this.getGlobalTracingHelper().isEnabled()}getTraceParent(e){return this.getGlobalTracingHelper().getTraceParent(e)}dispatchEngineSpans(e){return this.getGlobalTracingHelper().dispatchEngineSpans(e)}getActiveContext(){return this.getGlobalTracingHelper().getActiveContext()}runInChildSpan(e,r){return this.getGlobalTracingHelper().runInChildSpan(e,r)}getGlobalTracingHelper(){let e=globalThis[`V${Ul}_PRISMA_INSTRUMENTATION`],r=globalThis.PRISMA_INSTRUMENTATION;return e?.helper??r?.helper??Nl}};function Do(){return new ln}u();c();m();p();d();l();function Io(t,e=()=>{}){let r,n=new Promise(i=>r=i);return{then(i){return--t===0&&r(e()),i?.(n)}}}u();c();m();p();d();l();function Mo(t){return typeof t=="string"?t:t.reduce((e,r)=>{let n=typeof r=="string"?r:r.level;return n==="query"?e:e&&(r==="info"||e==="info")?"info":n},void 0)}u();c();m();p();d();l();var Lo=it(ei());u();c();m();p();d();l();function yr(t){return typeof t.batchRequestIdx=="number"}u();c();m();p();d();l();function _o(t){if(t.action!=="findUnique"&&t.action!=="findUniqueOrThrow")return;let e=[];return t.modelName&&e.push(t.modelName),t.query.arguments&&e.push(un(t.query.arguments)),e.push(un(t.query.selection)),e.join("")}function un(t){return`(${Object.keys(t).sort().map(r=>{let n=t[r];return typeof n=="object"&&n!==null?`(${r} ${un(n)})`:r}).join(" ")})`}u();c();m();p();d();l();var ql={aggregate:!1,aggregateRaw:!1,createMany:!0,createManyAndReturn:!0,createOne:!0,deleteMany:!0,deleteOne:!0,executeRaw:!0,findFirst:!1,findFirstOrThrow:!1,findMany:!1,findRaw:!1,findUnique:!1,findUniqueOrThrow:!1,groupBy:!1,queryRaw:!1,runCommandRaw:!0,updateMany:!0,updateManyAndReturn:!0,updateOne:!0,upsertOne:!0};function cn(t){return ql[t]}u();c();m();p();d();l();var hr=class{constructor(e){this.options=e;this.batches={}}batches;tickActive=!1;request(e){let r=this.options.batchBy(e);return r?(this.batches[r]||(this.batches[r]=[],this.tickActive||(this.tickActive=!0,g.nextTick(()=>{this.dispatchBatches(),this.tickActive=!1}))),new Promise((n,i)=>{this.batches[r].push({request:e,resolve:n,reject:i})})):this.options.singleLoader(e)}dispatchBatches(){for(let e in this.batches){let r=this.batches[e];delete this.batches[e],r.length===1?this.options.singleLoader(r[0].request).then(n=>{n instanceof Error?r[0].reject(n):r[0].resolve(n)}).catch(n=>{r[0].reject(n)}):(r.sort((n,i)=>this.options.batchOrder(n.request,i.request)),this.options.batchLoader(r.map(n=>n.request)).then(n=>{if(n instanceof Error)for(let i=0;i<r.length;i++)r[i].reject(n);else for(let i=0;i<r.length;i++){let o=n[i];o instanceof Error?r[i].reject(o):r[i].resolve(o)}}).catch(n=>{for(let i=0;i<r.length;i++)r[i].reject(n)}))}}get[Symbol.toStringTag](){return"DataLoader"}};u();c();m();p();d();l();l();function Le(t,e){if(e===null)return e;switch(t){case"bigint":return BigInt(e);case"bytes":{let{buffer:r,byteOffset:n,byteLength:i}=b.from(e,"base64");return new Uint8Array(r,n,i)}case"decimal":return new be(e);case"datetime":case"date":return new Date(e);case"time":return new Date(`1970-01-01T${e}Z`);case"bigint-array":return e.map(r=>Le("bigint",r));case"bytes-array":return e.map(r=>Le("bytes",r));case"decimal-array":return e.map(r=>Le("decimal",r));case"datetime-array":return e.map(r=>Le("datetime",r));case"date-array":return e.map(r=>Le("date",r));case"time-array":return e.map(r=>Le("time",r));default:return e}}function br(t){let e=[],r=Bl(t);for(let n=0;n<t.rows.length;n++){let i=t.rows[n],o={...r};for(let s=0;s<i.length;s++)o[t.columns[s]]=Le(t.types[s],i[s]);e.push(o)}return e}function Bl(t){let e={};for(let r=0;r<t.columns.length;r++)e[t.columns[r]]=null;return e}var Vl=G("prisma:client:request_handler"),wr=class{client;dataloader;logEmitter;constructor(e,r){this.logEmitter=r,this.client=e,this.dataloader=new hr({batchLoader:ao(async({requests:n,customDataProxyFetch:i})=>{let{transaction:o,otelParentCtx:s}=n[0],a=n.map(R=>R.protocolQuery),f=this.client._tracingHelper.getTraceParent(s),h=n.some(R=>cn(R.protocolQuery.action));return(await this.client._engine.requestBatch(a,{traceparent:f,transaction:jl(o),containsWrite:h,customDataProxyFetch:i})).map((R,k)=>{if(R instanceof Error)return R;try{return this.mapQueryEngineResult(n[k],R)}catch(A){return A}})}),singleLoader:async n=>{let i=n.transaction?.kind==="itx"?Fo(n.transaction):void 0,o=await this.client._engine.request(n.protocolQuery,{traceparent:this.client._tracingHelper.getTraceParent(),interactiveTransaction:i,isWrite:cn(n.protocolQuery.action),customDataProxyFetch:n.customDataProxyFetch});return this.mapQueryEngineResult(n,o)},batchBy:n=>n.transaction?.id?`transaction-${n.transaction.id}`:_o(n.protocolQuery),batchOrder(n,i){return n.transaction?.kind==="batch"&&i.transaction?.kind==="batch"?n.transaction.index-i.transaction.index:0}})}async request(e){try{return await this.dataloader.request(e)}catch(r){let{clientMethod:n,callsite:i,transaction:o,args:s,modelName:a}=e;this.handleAndLogRequestError({error:r,clientMethod:n,callsite:i,transaction:o,args:s,modelName:a,globalOmit:e.globalOmit})}}mapQueryEngineResult({dataPath:e,unpacker:r},n){let i=n?.data,o=this.unpack(i,e,r);return g.env.PRISMA_CLIENT_GET_TIME?{data:o}:o}handleAndLogRequestError(e){try{this.handleRequestError(e)}catch(r){throw this.logEmitter&&this.logEmitter.emit("error",{message:r.message,target:e.clientMethod,timestamp:new Date}),r}}handleRequestError({error:e,clientMethod:r,callsite:n,transaction:i,args:o,modelName:s,globalOmit:a}){if(Vl(e),$l(e,i))throw e;if(e instanceof Z&&Ql(e)){let h=Uo(e.meta);Zt({args:o,errors:[h],callsite:n,errorFormat:this.client._errorFormat,originalMethod:r,clientVersion:this.client._clientVersion,globalOmit:a})}let f=e.message;if(n&&(f=$t({callsite:n,originalMethod:r,isPanic:e.isPanic,showColors:this.client._errorFormat==="pretty",message:f})),f=this.sanitizeMessage(f),e.code){let h=s?{modelName:s,...e.meta}:e.meta;throw new Z(f,{code:e.code,clientVersion:this.client._clientVersion,meta:h,batchRequestIdx:e.batchRequestIdx})}else{if(e.isPanic)throw new xe(f,this.client._clientVersion);if(e instanceof Q)throw new Q(f,{clientVersion:this.client._clientVersion,batchRequestIdx:e.batchRequestIdx});if(e instanceof I)throw new I(f,this.client._clientVersion);if(e instanceof xe)throw new xe(f,this.client._clientVersion)}throw e.clientVersion=this.client._clientVersion,e}sanitizeMessage(e){return this.client._errorFormat&&this.client._errorFormat!=="pretty"?(0,Lo.default)(e):e}unpack(e,r,n){if(!e||(e.data&&(e=e.data),!e))return e;let i=Object.keys(e)[0],o=Object.values(e)[0],s=r.filter(h=>h!=="select"&&h!=="include"),a=Xr(o,s),f=i==="queryRaw"?br(a):et(a);return n?n(f):f}get[Symbol.toStringTag](){return"RequestHandler"}};function jl(t){if(t){if(t.kind==="batch")return{kind:"batch",options:{isolationLevel:t.isolationLevel}};if(t.kind==="itx")return{kind:"itx",options:Fo(t)};Me(t,"Unknown transaction kind")}}function Fo(t){return{id:t.id,payload:t.payload}}function $l(t,e){return yr(t)&&e?.kind==="batch"&&t.batchRequestIdx!==e.index}function Ql(t){return t.code==="P2009"||t.code==="P2012"}function Uo(t){if(t.kind==="Union")return{kind:"Union",errors:t.errors.map(Uo)};if(Array.isArray(t.selectionPath)){let[,...e]=t.selectionPath;return{...t,selectionPath:e}}return t}u();c();m();p();d();l();var No=yo;u();c();m();p();d();l();var $o=it(Br());u();c();m();p();d();l();var M=class extends Error{constructor(e){super(e+`
Read more at https://pris.ly/d/client-constructor`),this.name="PrismaClientConstructorValidationError"}get[Symbol.toStringTag](){return"PrismaClientConstructorValidationError"}};re(M,"PrismaClientConstructorValidationError");var qo=["datasources","datasourceUrl","errorFormat","adapter","log","transactionOptions","omit","__internal"],Bo=["pretty","colorless","minimal"],Vo=["info","query","warn","error"],Jl={datasources:(t,{datasourceNames:e})=>{if(t){if(typeof t!="object"||Array.isArray(t))throw new M(`Invalid value ${JSON.stringify(t)} for "datasources" provided to PrismaClient constructor`);for(let[r,n]of Object.entries(t)){if(!e.includes(r)){let i=rt(r,e)||` Available datasources: ${e.join(", ")}`;throw new M(`Unknown datasource ${r} provided to PrismaClient constructor.${i}`)}if(typeof n!="object"||Array.isArray(n))throw new M(`Invalid value ${JSON.stringify(t)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(n&&typeof n=="object")for(let[i,o]of Object.entries(n)){if(i!=="url")throw new M(`Invalid value ${JSON.stringify(t)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(typeof o!="string")throw new M(`Invalid value ${JSON.stringify(o)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`)}}}},adapter:(t,e)=>{if(!t&&Be(e.generator)==="client")throw new M('Using engine type "client" requires a driver adapter to be provided to PrismaClient constructor.');if(t===null)return;if(t===void 0)throw new M('"adapter" property must not be undefined, use null to conditionally disable driver adapters.');if(!gr(e).includes("driverAdapters"))throw new M('"adapter" property can only be provided to PrismaClient constructor when "driverAdapters" preview feature is enabled.');if(Be(e.generator)==="binary")throw new M('Cannot use a driver adapter with the "binary" Query Engine. Please use the "library" Query Engine.')},datasourceUrl:t=>{if(typeof t<"u"&&typeof t!="string")throw new M(`Invalid value ${JSON.stringify(t)} for "datasourceUrl" provided to PrismaClient constructor.
Expected string or undefined.`)},errorFormat:t=>{if(t){if(typeof t!="string")throw new M(`Invalid value ${JSON.stringify(t)} for "errorFormat" provided to PrismaClient constructor.`);if(!Bo.includes(t)){let e=rt(t,Bo);throw new M(`Invalid errorFormat ${t} provided to PrismaClient constructor.${e}`)}}},log:t=>{if(!t)return;if(!Array.isArray(t))throw new M(`Invalid value ${JSON.stringify(t)} for "log" provided to PrismaClient constructor.`);function e(r){if(typeof r=="string"&&!Vo.includes(r)){let n=rt(r,Vo);throw new M(`Invalid log level "${r}" provided to PrismaClient constructor.${n}`)}}for(let r of t){e(r);let n={level:e,emit:i=>{let o=["stdout","event"];if(!o.includes(i)){let s=rt(i,o);throw new M(`Invalid value ${JSON.stringify(i)} for "emit" in logLevel provided to PrismaClient constructor.${s}`)}}};if(r&&typeof r=="object")for(let[i,o]of Object.entries(r))if(n[i])n[i](o);else throw new M(`Invalid property ${i} for "log" provided to PrismaClient constructor`)}},transactionOptions:t=>{if(!t)return;let e=t.maxWait;if(e!=null&&e<=0)throw new M(`Invalid value ${e} for maxWait in "transactionOptions" provided to PrismaClient constructor. maxWait needs to be greater than 0`);let r=t.timeout;if(r!=null&&r<=0)throw new M(`Invalid value ${r} for timeout in "transactionOptions" provided to PrismaClient constructor. timeout needs to be greater than 0`)},omit:(t,e)=>{if(typeof t!="object")throw new M('"omit" option is expected to be an object.');if(t===null)throw new M('"omit" option can not be `null`');let r=[];for(let[n,i]of Object.entries(t)){let o=Wl(n,e.runtimeDataModel);if(!o){r.push({kind:"UnknownModel",modelKey:n});continue}for(let[s,a]of Object.entries(i)){let f=o.fields.find(h=>h.name===s);if(!f){r.push({kind:"UnknownField",modelKey:n,fieldName:s});continue}if(f.relationName){r.push({kind:"RelationInOmit",modelKey:n,fieldName:s});continue}typeof a!="boolean"&&r.push({kind:"InvalidFieldValue",modelKey:n,fieldName:s})}}if(r.length>0)throw new M(Kl(t,r))},__internal:t=>{if(!t)return;let e=["debug","engine","configOverride"];if(typeof t!="object")throw new M(`Invalid value ${JSON.stringify(t)} for "__internal" to PrismaClient constructor`);for(let[r]of Object.entries(t))if(!e.includes(r)){let n=rt(r,e);throw new M(`Invalid property ${JSON.stringify(r)} for "__internal" provided to PrismaClient constructor.${n}`)}}};function Qo(t,e){for(let[r,n]of Object.entries(t)){if(!qo.includes(r)){let i=rt(r,qo);throw new M(`Unknown property ${r} provided to PrismaClient constructor.${i}`)}Jl[r](n,e)}if(t.datasourceUrl&&t.datasources)throw new M('Can not use "datasourceUrl" and "datasources" options at the same time. Pick one of them')}function rt(t,e){if(e.length===0||typeof t!="string")return"";let r=Gl(t,e);return r?` Did you mean "${r}"?`:""}function Gl(t,e){if(e.length===0)return null;let r=e.map(i=>({value:i,distance:(0,$o.default)(t,i)}));r.sort((i,o)=>i.distance<o.distance?-1:1);let n=r[0];return n.distance<3?n.value:null}function Wl(t,e){return jo(e.models,t)??jo(e.types,t)}function jo(t,e){let r=Object.keys(t).find(n=>ve(n)===e);if(r)return t[r]}function Kl(t,e){let r=He(t);for(let o of e)switch(o.kind){case"UnknownModel":r.arguments.getField(o.modelKey)?.markAsError(),r.addErrorMessage(()=>`Unknown model name: ${o.modelKey}.`);break;case"UnknownField":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>`Model "${o.modelKey}" does not have a field named "${o.fieldName}".`);break;case"RelationInOmit":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>'Relations are already excluded by default and can not be specified in "omit".');break;case"InvalidFieldValue":r.arguments.getDeepFieldValue([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>"Omit field option value must be a boolean.");break}let{message:n,args:i}=Xt(r,"colorless");return`Error validating "omit" option:

${i}

${n}`}u();c();m();p();d();l();function Jo(t){return t.length===0?Promise.resolve([]):new Promise((e,r)=>{let n=new Array(t.length),i=null,o=!1,s=0,a=()=>{o||(s++,s===t.length&&(o=!0,i?r(i):e(n)))},f=h=>{o||(o=!0,r(h))};for(let h=0;h<t.length;h++)t[h].then(C=>{n[h]=C,a()},C=>{if(!yr(C)){f(C);return}C.batchRequestIdx===h?f(C):(i||(i=C),a())})})}var Ae=G("prisma:client");typeof globalThis=="object"&&(globalThis.NODE_CLIENT=!0);var Hl={requestArgsToMiddlewareArgs:t=>t,middlewareArgsToRequestArgs:t=>t},zl=Symbol.for("prisma.client.transaction.id"),Yl={id:0,nextId(){return++this.id}};function Ko(t){class e{_originalClient=this;_runtimeDataModel;_requestHandler;_connectionPromise;_disconnectionPromise;_engineConfig;_accelerateEngineConfig;_clientVersion;_errorFormat;_tracingHelper;_previewFeatures;_activeProvider;_globalOmit;_extensions;_engine;_appliedParent;_createPrismaPromise=an();constructor(n){t=n?.__internal?.configOverride?.(t)??t,po(t),n&&Qo(n,t);let i=new sr().on("error",()=>{});this._extensions=ze.empty(),this._previewFeatures=gr(t),this._clientVersion=t.clientVersion??No,this._activeProvider=t.activeProvider,this._globalOmit=n?.omit,this._tracingHelper=Do();let o=t.relativeEnvPaths&&{rootEnvPath:t.relativeEnvPaths.rootEnvPath&&Ut.resolve(t.dirname,t.relativeEnvPaths.rootEnvPath),schemaEnvPath:t.relativeEnvPaths.schemaEnvPath&&Ut.resolve(t.dirname,t.relativeEnvPaths.schemaEnvPath)},s;if(n?.adapter){s=n.adapter;let f=t.activeProvider==="postgresql"||t.activeProvider==="cockroachdb"?"postgres":t.activeProvider;if(s.provider!==f)throw new I(`The Driver Adapter \`${s.adapterName}\`, based on \`${s.provider}\`, is not compatible with the provider \`${f}\` specified in the Prisma schema.`,this._clientVersion);if(n.datasources||n.datasourceUrl!==void 0)throw new I("Custom datasource configuration is not compatible with Prisma Driver Adapters. Please define the database connection string directly in the Driver Adapter configuration.",this._clientVersion)}let a=t.injectableEdgeEnv?.();try{let f=n??{},h=f.__internal??{},C=h.debug===!0;C&&G.enable("prisma:client");let R=Ut.resolve(t.dirname,t.relativePath);Nn.existsSync(R)||(R=t.dirname),Ae("dirname",t.dirname),Ae("relativePath",t.relativePath),Ae("cwd",R);let k=h.engine||{};if(f.errorFormat?this._errorFormat=f.errorFormat:g.env.NODE_ENV==="production"?this._errorFormat="minimal":g.env.NO_COLOR?this._errorFormat="colorless":this._errorFormat="colorless",this._runtimeDataModel=t.runtimeDataModel,this._engineConfig={cwd:R,dirname:t.dirname,enableDebugLogs:C,allowTriggerPanic:k.allowTriggerPanic,prismaPath:k.binaryPath??void 0,engineEndpoint:k.endpoint,generator:t.generator,showColors:this._errorFormat==="pretty",logLevel:f.log&&Mo(f.log),logQueries:f.log&&!!(typeof f.log=="string"?f.log==="query":f.log.find(A=>typeof A=="string"?A==="query":A.level==="query")),env:a?.parsed??{},flags:[],engineWasm:t.engineWasm,compilerWasm:t.compilerWasm,clientVersion:t.clientVersion,engineVersion:t.engineVersion,previewFeatures:this._previewFeatures,activeProvider:t.activeProvider,inlineSchema:t.inlineSchema,overrideDatasources:fo(f,t.datasourceNames),inlineDatasources:t.inlineDatasources,inlineSchemaHash:t.inlineSchemaHash,tracingHelper:this._tracingHelper,transactionOptions:{maxWait:f.transactionOptions?.maxWait??2e3,timeout:f.transactionOptions?.timeout??5e3,isolationLevel:f.transactionOptions?.isolationLevel},logEmitter:i,isBundled:t.isBundled,adapter:s},this._accelerateEngineConfig={...this._engineConfig,accelerateUtils:{resolveDatasourceUrl:dr,getBatchRequestPayload:ur,prismaGraphQLToJSError:cr,PrismaClientUnknownRequestError:Q,PrismaClientInitializationError:I,PrismaClientKnownRequestError:Z,debug:G("prisma:client:accelerateEngine"),engineVersion:Wo.version,clientVersion:t.clientVersion}},Ae("clientVersion",t.clientVersion),this._engine=xo(t,this._engineConfig),this._requestHandler=new wr(this,i),f.log)for(let A of f.log){let _=typeof A=="string"?A:A.emit==="stdout"?A.level:null;_&&this.$on(_,O=>{lt.log(`${lt.tags[_]??""}`,O.message||O.query)})}}catch(f){throw f.clientVersion=this._clientVersion,f}return this._appliedParent=Tt(this)}get[Symbol.toStringTag](){return"PrismaClient"}$on(n,i){return n==="beforeExit"?this._engine.onBeforeExit(i):n&&this._engineConfig.logEmitter.on(n,i),this}$connect(){try{return this._engine.start()}catch(n){throw n.clientVersion=this._clientVersion,n}}async $disconnect(){try{await this._engine.stop()}catch(n){throw n.clientVersion=this._clientVersion,n}finally{Un()}}$executeRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"executeRaw",args:o,transaction:n,clientMethod:i,argsMapper:sn({clientMethod:i,activeProvider:a}),callsite:Ce(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$executeRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0){let[s,a]=Go(n,i);return on(this._activeProvider,s.text,s.values,Array.isArray(n)?"prisma.$executeRaw`<SQL>`":"prisma.$executeRaw(sql`<SQL>`)"),this.$executeRawInternal(o,"$executeRaw",s,a)}throw new K("`$executeRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#executeraw\n",{clientVersion:this._clientVersion})})}$executeRawUnsafe(n,...i){return this._createPrismaPromise(o=>(on(this._activeProvider,n,i,"prisma.$executeRawUnsafe(<SQL>, [...values])"),this.$executeRawInternal(o,"$executeRawUnsafe",[n,...i])))}$runCommandRaw(n){if(t.activeProvider!=="mongodb")throw new K(`The ${t.activeProvider} provider does not support $runCommandRaw. Use the mongodb provider.`,{clientVersion:this._clientVersion});return this._createPrismaPromise(i=>this._request({args:n,clientMethod:"$runCommandRaw",dataPath:[],action:"runCommandRaw",argsMapper:Eo,callsite:Ce(this._errorFormat),transaction:i}))}async $queryRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"queryRaw",args:o,transaction:n,clientMethod:i,argsMapper:sn({clientMethod:i,activeProvider:a}),callsite:Ce(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$queryRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0)return this.$queryRawInternal(o,"$queryRaw",...Go(n,i));throw new K("`$queryRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#queryraw\n",{clientVersion:this._clientVersion})})}$queryRawTyped(n){return this._createPrismaPromise(i=>{if(!this._hasPreviewFlag("typedSql"))throw new K("`typedSql` preview feature must be enabled in order to access $queryRawTyped API",{clientVersion:this._clientVersion});return this.$queryRawInternal(i,"$queryRawTyped",n)})}$queryRawUnsafe(n,...i){return this._createPrismaPromise(o=>this.$queryRawInternal(o,"$queryRawUnsafe",[n,...i]))}_transactionWithArray({promises:n,options:i}){let o=Yl.nextId(),s=Io(n.length),a=n.map((f,h)=>{if(f?.[Symbol.toStringTag]!=="PrismaPromise")throw new Error("All elements of the array need to be Prisma Client promises. Hint: Please make sure you are not awaiting the Prisma client calls you intended to pass in the $transaction function.");let C=i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel,R={kind:"batch",id:o,index:h,isolationLevel:C,lock:s};return f.requestTransaction?.(R)??f});return Jo(a)}async _transactionWithCallback({callback:n,options:i}){let o={traceparent:this._tracingHelper.getTraceParent()},s={maxWait:i?.maxWait??this._engineConfig.transactionOptions.maxWait,timeout:i?.timeout??this._engineConfig.transactionOptions.timeout,isolationLevel:i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel},a=await this._engine.transaction("start",o,s),f;try{let h={kind:"itx",...a};f=await n(this._createItxClient(h)),await this._engine.transaction("commit",o,a)}catch(h){throw await this._engine.transaction("rollback",o,a).catch(()=>{}),h}return f}_createItxClient(n){return ae(Tt(ae(Yi(this),[H("_appliedParent",()=>this._appliedParent._createItxClient(n)),H("_createPrismaPromise",()=>an(n)),H(zl,()=>n.id)])),[Xe(ro)])}$transaction(n,i){let o;typeof n=="function"?this._engineConfig.adapter?.adapterName==="@prisma/adapter-d1"?o=()=>{throw new Error("Cloudflare D1 does not support interactive transactions. We recommend you to refactor your queries with that limitation in mind, and use batch transactions with `prisma.$transactions([])` where applicable.")}:o=()=>this._transactionWithCallback({callback:n,options:i}):o=()=>this._transactionWithArray({promises:n,options:i});let s={name:"transaction",attributes:{method:"$transaction"}};return this._tracingHelper.runInChildSpan(s,o)}_request(n){n.otelParentCtx=this._tracingHelper.getActiveContext();let i=n.middlewareArgsMapper??Hl,o={args:i.requestArgsToMiddlewareArgs(n.args),dataPath:n.dataPath,runInTransaction:!!n.transaction,action:n.action,model:n.model},s={operation:{name:"operation",attributes:{method:o.action,model:o.model,name:o.model?`${o.model}.${o.action}`:o.action}}},a=async f=>{let{runInTransaction:h,args:C,...R}=f,k={...n,...R};C&&(k.args=i.middlewareArgsToRequestArgs(C)),n.transaction!==void 0&&h===!1&&delete k.transaction;let A=await so(this,k);return k.model?to({result:A,modelName:k.model,args:k.args,extensions:this._extensions,runtimeDataModel:this._runtimeDataModel,globalOmit:this._globalOmit}):A};return this._tracingHelper.runInChildSpan(s.operation,()=>a(o))}async _executeRequest({args:n,clientMethod:i,dataPath:o,callsite:s,action:a,model:f,argsMapper:h,transaction:C,unpacker:R,otelParentCtx:k,customDataProxyFetch:A}){try{n=h?h(n):n;let _={name:"serialize"},O=this._tracingHelper.runInChildSpan(_,()=>nr({modelName:f,runtimeDataModel:this._runtimeDataModel,action:a,args:n,clientMethod:i,callsite:s,extensions:this._extensions,errorFormat:this._errorFormat,clientVersion:this._clientVersion,previewFeatures:this._previewFeatures,globalOmit:this._globalOmit}));return G.enabled("prisma:client")&&(Ae("Prisma Client call:"),Ae(`prisma.${i}(${Vi(n)})`),Ae("Generated request:"),Ae(JSON.stringify(O,null,2)+`
`)),C?.kind==="batch"&&await C.lock,this._requestHandler.request({protocolQuery:O,modelName:f,action:a,clientMethod:i,dataPath:o,callsite:s,args:n,extensions:this._extensions,transaction:C,unpacker:R,otelParentCtx:k,otelChildCtx:this._tracingHelper.getActiveContext(),globalOmit:this._globalOmit,customDataProxyFetch:A})}catch(_){throw _.clientVersion=this._clientVersion,_}}$metrics=new Ye(this);_hasPreviewFlag(n){return!!this._engineConfig.previewFeatures?.includes(n)}$applyPendingMigrations(){return this._engine.applyPendingMigrations()}$extends=Xi}return e}function Go(t,e){return Xl(t)?[new ee(t,e),So]:[t,Oo]}function Xl(t){return Array.isArray(t)&&Array.isArray(t.raw)}u();c();m();p();d();l();var Zl=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function Ho(t){return new Proxy(t,{get(e,r){if(r in e)return e[r];if(!Zl.has(r))throw new TypeError(`Invalid enum value: ${String(r)}`)}})}u();c();m();p();d();l();l();0&&(module.exports={DMMF,Debug,Decimal,Extensions,MetricsClient,PrismaClientInitializationError,PrismaClientKnownRequestError,PrismaClientRustPanicError,PrismaClientUnknownRequestError,PrismaClientValidationError,Public,Sql,createParam,defineDmmfProperty,deserializeJsonResponse,deserializeRawResult,dmmfToRuntimeDataModel,empty,getPrismaClient,getRuntime,join,makeStrictEnum,makeTypedQueryFactory,objectEnumValues,raw,serializeJsonQuery,skip,sqltag,warnEnvConflicts,warnOnce});
//# sourceMappingURL=wasm-engine-edge.js.map
