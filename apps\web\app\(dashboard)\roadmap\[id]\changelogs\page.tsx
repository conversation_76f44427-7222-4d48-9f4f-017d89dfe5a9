import { Suspense } from "react";
import { dehydrate } from "@tanstack/react-query";
import { getAllRoadmapChangelogs } from "@/actions/roadmap/changelogs";
import { getRoadmap } from "@/actions/roadmap";
import getQueryClient from "@/lib/query/getQueryClient";
import Hydrate from "@/lib/query/hydrate.client";
import { ChangelogsClient } from "./_components/changelogs-client";
import LoadingSpinner from "@workspace/ui/components/loading-spinner";

interface ChangelogsPageProps {
  params: Promise<{ id: string }>;
}

export default async function ChangelogsPage({ params }: ChangelogsPageProps) {
  const { id } = await params;
  const queryClient = getQueryClient();

  // Pre-fetch roadmap and changelogs data in parallel
  await Promise.all([
    queryClient.prefetchQuery({
      queryKey: ["roadmap", id],
      queryFn: () => getRoadmap(id),
    }),
    queryClient.prefetchQuery({
      queryKey: ["roadmapChangelogs", id],
      queryFn: () => getAllRoadmapChangelogs(id),
    }),
  ]);

  const dehydratedState = dehydrate(queryClient);

  return (
    <Hydrate state={dehydratedState}>
      <Suspense fallback={<LoadingSpinner />}>
        <ChangelogsClient roadmapId={id} />
      </Suspense>
    </Hydrate>
  );
}
