enum ActivityType {
    CREATED
    UPDATED
    PHASE_CHANGED
    ASSIGNED
    UNASSIGNED
    DEPENDENCY_ADDED
    DEPENDENCY_REMOVED
    LINK_ADDED
    LINK_REMOVED
    PARENT_CHANGED
}

enum EntityType {
    PROJECT
    FEATURE
    ISSUE
    IDEA
    ROADMAP
    MILESTONE
}

enum IdeaStatus {
    INVALIDATED
    VALIDATED
    FAILED
    IN_PROGRESS
    LAUNCHED
}

enum Importance {
    CRITICAL
    HIGH
    MEDIUM
    LOW
}

enum ProjectPlatform {
    web
    mobile
    both
    api
    plugin
    desktop
    cli
}

enum ProjectStatus {
    planning
    in_progress
    review
    completed
}

enum IssueStatus {
    BACKLOG
    IN_PROGRESS
    IN_REVIEW
    DONE
    BLOCKED
    CANCELLED
}

enum IssueLabel {
    UI
    BUG
    FEATURE
    IMPROVEMENT
    TASK
    DOCUMENTATION
    REFACTOR
    PERFORMANCE
    DESIGN
    SECURITY
    ACCESSIBILITY
    TESTING
    INTERNATIONALIZATION
}

enum AssetType {
    image
    document
    video
    link
    code
    design
    other
}

enum LinkType {
    youtube
    figma
    notion
    github
    dribbble
    behance
    external
}

enum AssetCategory {
    branding
    ui_design
    mockups
    documentation
    inspiration
    code_snippets
    presentations
    tutorials
    other
}

enum RoadmapFeedbackSentiment {
    positive
    neutral
    negative
}

enum FeatureRequestStatus {
    pending
    under_review
    approved
    rejected
    implemented
}

enum FeatureRequestPriority {
    low
    medium
    high
    urgent
}

enum FeaturePhase {
    DISCOVERY
    PLANNING
    DEVELOPMENT
    TESTING
    DEPLOYMENT
    COMPLETED
    RELEASE
    LIVE
    DEPRECATED
}

enum MilestoneStatus {
    NOT_STARTED
    IN_PROGRESS
    AT_RISK
    COMPLETED
    DELAYED
}

enum IntegrationType {
    RESEND
    LOOPS
    SENDGRID
    MAILCHIMP
    CONVERTKIT
    GITHUB
}

enum ApiPermission {
    READ
    WRITE
    DELETE
    ADMIN
}

enum ChangelogEntryType {
    FEATURE
    FIX
    IMPROVEMENT
    BREAKING
    SECURITY
    DEPRECATION
    DOCUMENTATION
    PERFORMANCE
}

enum SwotType {
    Strength
    Weakness
    Opportunity
    Threat
}

// Validation System Enums
enum ValidationStatus {
    PENDING
    IN_PROGRESS
    COMPLETED
    FAILED
    REQUIRES_REVIEW
}

enum ValidationScore {
    EXCELLENT
    GOOD
    AVERAGE
    POOR
    CRITICAL
}

enum MarketSize {
    NICHE
    SMALL
    MEDIUM
    LARGE
    MASSIVE
}

enum MarketGrowthRate {
    DECLINING
    STAGNANT
    SLOW_GROWTH
    MODERATE_GROWTH
    RAPID_GROWTH
    EXPLOSIVE_GROWTH
}

enum MarketMaturity {
    EMERGING
    GROWTH
    MATURE
    DECLINING
    TRANSFORMING
}

enum CompetitionLevel {
    NONE
    LOW
    MODERATE
    HIGH
    SATURATED
}

enum CustomerSegment {
    B2B_ENTERPRISE
    B2B_SMB
    B2C_CONSUMER
    B2C_PROSUMER
    B2G_GOVERNMENT
    MARKETPLACE
    PLATFORM
}

enum RevenueModel {
    SUBSCRIPTION
    FREEMIUM
    ONE_TIME_PURCHASE
    TRANSACTION_FEE
    ADVERTISING
    COMMISSION
    LICENSING
    USAGE_BASED
    HYBRID
}

enum PricingStrategy {
    PENETRATION
    SKIMMING
    COMPETITIVE
    VALUE_BASED
    COST_PLUS
    DYNAMIC
}

enum RiskLevel {
    MINIMAL
    LOW
    MODERATE
    HIGH
    EXTREME
}

enum RiskCategory {
    MARKET
    TECHNICAL
    FINANCIAL
    REGULATORY
    COMPETITIVE
    OPERATIONAL
    TEAM
    TIMING
}

enum FundingStage {
    BOOTSTRAPPED
    PRE_SEED
    SEED
    SERIES_A
    SERIES_B
    SERIES_C_PLUS
    IPO_READY
}

enum InvestmentRecommendation {
    STRONG_BUY
    BUY
    HOLD
    WEAK_BUY
    AVOID
}

enum GoToMarketStrategy {
    DIRECT_SALES
    INBOUND_MARKETING
    OUTBOUND_MARKETING
    PARTNERSHIPS
    VIRAL_GROWTH
    COMMUNITY_DRIVEN
    PRODUCT_LED_GROWTH
    CHANNEL_PARTNERS
}

enum ValidationMethod {
    SURVEYS
    INTERVIEWS
    LANDING_PAGE_TEST
    MVP_TEST
    PROTOTYPE_TEST
    MARKET_RESEARCH
    COMPETITOR_ANALYSIS
    EXPERT_CONSULTATION
}

enum FinancialMetric {
    CAC
    LTV
    ARPU
    MRR
    ARR
    CHURN_RATE
    GROSS_MARGIN
    BURN_RATE
    RUNWAY
    BREAK_EVEN
}
