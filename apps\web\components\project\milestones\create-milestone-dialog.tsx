"use client";

import { useState } from "react";
import { useSession } from "@/context/session-context";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@workspace/ui/components/dialog";
import { toast } from "sonner";
import { DateRangeSelector } from "@/components/ui/selectors/date-range-selector";
import { AssigneeSelector } from "@/components/ui/selectors/assignee-selector";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { createMilestone } from "@/actions/project/milestone";
import { MilestoneStatusType } from "@workspace/backend";

// Types for milestone operations
export interface CreateMilestoneData {
  name: string;
  description?: string;
  projectId: string;
  startDate?: number;
  endDate?: number;
  ownerId?: string;
  status?: MilestoneStatusType;
}

export interface UpdateMilestoneData {
  name?: string;
  description?: string;
  startDate?: number | null;
  endDate?: number | null;
  ownerId?: string | null;
  status?: MilestoneStatusType;
}

export interface MilestoneWithProgress {
  id: string;
  name: string;
  description: string | null;
  status: MilestoneStatusType;
  startDate: Date | null;
  endDate: Date | null;
  createdAt: Date;
  updatedAt: Date;
  projectId: string;
  organizationId: string;
  ownerId: string | null;
  owner: { id: string; name: string; image: string | null } | null;
  progress: number;
  completedIssueCount: number;
  issueCount: number;
  completedFeatureCount: number;
  featureCount: number;
  overdueItems: number;
  dependsOn: { id: string; name: string }[];
  blocking: { id: string; name: string }[];
  issues: any[];
  features: any[];
}

interface CreateMilestoneDialogProps {
  projectId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

type FormState = {
  name: string;
  description: string;
  startDate: Date | undefined;
  endDate: Date | undefined;
  ownerId: string | undefined;
};

export function CreateMilestoneDialog({
  projectId,
  open,
  onOpenChange,
}: CreateMilestoneDialogProps) {
  const [form, setForm] = useState<FormState>({
    name: "",
    description: "",
    startDate: new Date(),
    endDate: undefined,
    ownerId: undefined,
  });

  const queryClient = useQueryClient();

  // Utility function to validate form fields
  const validateForm = (form: FormState) => {
    if (!form.name.trim()) {
      toast.error("Milestone name is required");
      return false;
    }
    return true;
  };

  const close = () => {
    onOpenChange(false);
    // Reset form
    setForm({
      name: "",
      description: "",
      startDate: new Date(),
      endDate: undefined,
      ownerId: undefined,
    });
  };

  const mutation = useMutation({
    mutationFn: async (data: CreateMilestoneData) => createMilestone(data),
    onSuccess: () => {
      toast.success("Milestone created successfully");
      // Invalidate and refetch the milestones query
      queryClient.invalidateQueries({ queryKey: ["milestones", projectId] });
      close();
    },
    onError: () => {
      toast.error("Failed to create milestone");
    },
  });

  async function handleSubmit() {
    if (!validateForm(form)) {
      return;
    }
    mutation.mutate({
      name: form.name.trim(),
      description: form.description.trim() || undefined,
      projectId,
      startDate: form.startDate?.getTime(),
      endDate: form.endDate?.getTime(),
      ownerId: form.ownerId,
    });
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Create Milestone
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name" className="text-sm font-medium">
              Name *
            </Label>
            <Input
              id="name"
              placeholder="Enter milestone name"
              value={form.name}
              onChange={(e) => setForm({ ...form, name: e.target.value })}
              className="h-10"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-medium">
              Description
            </Label>
            <Textarea
              id="description"
              placeholder="Enter milestone description"
              value={form.description}
              onChange={(e) =>
                setForm({ ...form, description: e.target.value })
              }
              className="min-h-[80px] resize-none"
              rows={3}
            />
          </div>

          <div className="flex items-center gap-4">
            <AssigneeSelector
              assignee={form.ownerId ?? null}
              onChange={(value) => setForm({ ...form, ownerId: value })}
            />
            <DateRangeSelector
              startDate={form.startDate}
              endDate={form.endDate}
              onRangeChange={(startDate, endDate) =>
                setForm({ ...form, startDate, endDate })
              }
              placeholder="Select milestone duration"
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            className="mb-2"
            type="button"
            variant="outline"
            onClick={close}
          >
            Cancel
          </Button>
          <Button
            className="mb-2"
            onClick={handleSubmit}
            disabled={mutation.status === "pending"}
          >
            {mutation.status === "pending" ? "Creating..." : "Create Milestone"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
